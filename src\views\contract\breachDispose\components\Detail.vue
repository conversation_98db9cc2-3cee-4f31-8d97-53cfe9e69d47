<template>
  <a-drawer v-model:open="visible" class="common-detail-drawer" placement="right" width="1000px" @close="handleClose">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitch(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitch(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleViewContract">查看合同</a-menu-item>
                <a-menu-item @click="handleUnAudit">反审批</a-menu-item>
                <a-menu-item @click="handleDelete">删除</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">合同违约处置</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.updateBy_dictText }} 提交于{{ detail.updateTime }}</span>
      </div>
      <h4 class="text-[16px] font-bold mb-[12px]">合同基础信息</h4>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">业务时间: {{ detail.bizDate }}</span>
        <span class="w-[50%]">违约金额: {{ detail.amount ? `${detail.amount}元` : '' }}</span>
        <span class="w-[50%]">合同编码: {{ detail.contractNumber }}</span>
        <span class="w-[50%]">签约客户: {{ detail.customer_dictText }}</span>
        <span class="w-[50%]">签约日期: {{ detail.signDate }}</span>
        <span class="w-[50%]">管理公司: {{ detail.manageCompany_dictText }}</span>
        <span class="w-[50%]">合同类型: {{ detail.contractType_dictText }}</span>
        <span class="w-[50%]">业务员: {{ detail.operator_dictText }}</span>
        <span class="w-[50%]">合同开始日期: {{ detail.startDate }}</span>
        <span class="w-[50%]">合同结束日期: {{ detail.expireDate }}</span>
        <span class="w-[50%]">应付日期: {{ detail.expireDate }}</span>
        <span class="w-[50%]">付款日期: {{ detail.expireDate }}</span>
      </div>
      <h4 class="text-[16px] font-bold mt-[40px] mb-[12px]">违约租赁单元</h4>
      <a-table :data-source="leaseUnitList" :columns="leaseUnitColumns" :pagination="false" :scroll="{ x: 1500 }">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <span class="primary-btn mr-[10px]" @click="viewUnitDetail(record)">单元详情</span>
          </template>
        </template>
      </a-table>
      <h4 class="text-[16px] font-bold mt-[44px] mb-[12px]">附件</h4>
      <file-list :biz-id="detail.id"></file-list>
    </a-spin>
    <lease-unit-detail
      ref="leaseUnitDetailRef"
      :data-list="leaseUnitList.map((i) => ({ id: i.leaseUnit }))"
    ></lease-unit-detail>
    <contract-detail ref="contractDetailRef" :data-list="[]"></contract-detail>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail, deleteBatch, unAudit, queryBreachLeaseUnit } from '../apis.js'
import { Modal, message } from 'ant-design-vue'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'

const { dataList } = defineProps({
  dataList: { required: true, type: Array }
})

const emit = defineEmits(['editRentScheme', 'refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const currentIndex = ref(0)
const handleSwitch = (index) => {
  loadDetail(dataList[index].id)
}

const loading = ref(false)
const detail = reactive({
  id: ''
})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  await loadLeaseUnitList(result.contract)
  loading.value = false
}

const leaseUnitList = ref([])
const loadLeaseUnitList = async (contract) => {
  const { result } = await queryBreachLeaseUnit({ contract })
  leaseUnitList.value = result
}

const leaseUnitColumns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit_dictText', fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁面积m²', dataIndex: 'leaseArea' },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  { title: '消防等级', dataIndex: 'firefightingRate_dictText' },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const leaseUnitDetailRef = ref()
const viewUnitDetail = (data) => {
  leaseUnitDetailRef.value.open({ id: data.leaseUnit })
}

const contractDetailRef = ref()
const handleViewContract = () => {
  contractDetailRef.value.open(detail.contract)
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail.id)
}

const handleUnAudit = () => {
  Modal.confirm({
    title: '是否确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      await unAudit({ id: detail.id })
      message.success('反审核成功')
      loadDetail(detail.id)
      emit('refresh')
    }
  })
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除该合同违约处置？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>
