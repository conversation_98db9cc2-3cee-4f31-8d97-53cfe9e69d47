{"name": "park-management", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode dev", "build:uat": "vite build --mode uat", "build:prod": "vite build --mode prod", "preview": "vite preview", "prepare": "husky install"}, "lint-staged": {"*.{js,vue}": ["eslint --fix", "eslint"]}, "dependencies": {"ant-design-vue": "4.x", "axios": "^1.8.4", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "echarts": "^5.6.0", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^3.2.3", "tailwindcss": "^4.1.4", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "17", "@commitlint/config-conventional": "17", "@eslint/config-helpers": "^0.2.1", "@eslint/css": "^0.7.0", "@eslint/js": "^9.25.0", "@tailwindcss/vite": "^4.1.4", "@vitejs/plugin-vue": "^5.2.2", "eslint": "^9.25.0", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "husky": "8", "less": "^4.3.0", "less-loader": "^12.2.0", "lint-staged": "11", "prettier": "^3.5.3", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.1"}}