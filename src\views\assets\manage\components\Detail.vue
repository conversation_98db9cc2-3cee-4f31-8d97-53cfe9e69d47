<template>
  <a-drawer v-model:open="visible" :mask-closable="false" class="common-detail-drawer" placement="right" width="1200px">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === 0 }"
            @click="handleSwitch(1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === ids.length - 1 }"
            @click="handleSwitch()"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <!-- 暂存、撤回、不通过的才能编辑提交？ -->
          <span v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)" class="primary-btn" @click="handleEdit">
            编辑
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn" @click="turnToPage(1)">资产处置</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="turnToPage(2)">资产跟踪</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="handleDel">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">资产详情：{{ detailData.name }}</h2>
        <a-tag :type="getStatusColor(detailData.status)">
          {{ detailData.status_dictText }}
        </a-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>
      <!-- 锚点导航 -->
      <anchor-tabs :tab-list="navList" height="calc(100vh - 295px)">
        <template #baseInfo>
          <h2 class="text-[16px] font-bold mb-[12px]">资产照片</h2>
          <div class="flex flex-wrap gap-y-[12px] gap-x-[12px] text-[#495A7A]">
            <div class="text-center">
              <a-image :width="120" :src="detailData.ownerCover"></a-image>
              <div>产权封面</div>
            </div>
            <div class="text-center">
              <a-image :width="120" :src="detailData.ownerInside"></a-image>
              <div>产权证内页</div>
            </div>
            <div class="text-center">
              <a-image :width="120" :src="detailData.ownerRealImage"></a-image>
              <div>资产外观实拍</div>
            </div>
          </div>
          <h2 class="text-[16px] font-bold mb-[12px] mt-[12px]">资产基础信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">资产名称：{{ detailData.name || '-' }}</span>
            <span class="w-[50%]">产权分类：{{ detailData.treeId_dictText || '-' }}</span>
            <span class="w-[50%]">关联项目楼栋：{{ detailData.collectionCompany || '-' }}</span>
            <span class="w-[50%]">地址：{{ detailData.detailAddress || '-' }}</span>
            <span class="w-[50%]">租金归集公司：{{ detailData.collectionCompany_dictText || '-' }}</span>
            <span class="w-[50%]">资产权属公司：{{ detailData.ownerCompany_dictText || '-' }}</span>
            <span class="w-[50%]">物业管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
            <span class="w-[50%]">资产类型：{{ renderDict(detailData.assetsType, 'CT_BAS_AssetsType') || '-' }}</span>
            <span class="w-[50%]">
              业务状态：{{ renderDict(detailData.bizStatus, 'CT_BASE_ENUM_HouseOwner_BizStatus') || '-' }}
            </span>
            <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
          </div>
          <h2 class="text-[16px] font-bold mb-[12px] mt-[12px]">产权信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">产权号：{{ detailData.ownerNumber || '-' }}</span>
            <span class="w-[50%]">权证获得日期：{{ detailData.warrantsDate || '-' }}</span>
            <span class="w-[50%]">
              取得来源：{{ renderDict(detailData.acquisitionMethod, 'CT_BAS_AcquisitionMethod') || '-' }}
            </span>
            <span class="w-[50%]">
              产权情况：{{
                renderDict(detailData.propertyRightStatus, 'CT_BASE_ENUM_HouseOwner_PropertyRightStatus') || '-'
              }}
            </span>
            <span class="w-[50%]">代管委托方：{{ detailData.proxies || '-' }}</span>
            <span class="w-[50%]">使用权类型：{{ renderDict(detailData.landNature, 'CT_BAS_LandNature') || '-' }}</span>
          </div>
          <h2 class="text-[16px] font-bold mb-[12px] mt-[12px]">土地信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">土地取得价格：{{ detailData.landPrice || '-' }}</span>
            <span class="w-[50%]">
              土地建设情况：{{ renderDict(detailData.landConstructionSituation, 'CT_BAS_LandCS') || '-' }}
            </span>
            <span class="w-[50%]">租赁土地租金：{{ detailData.landRent || '-' }}</span>
            <span class="w-[50%]">地价款（租金）欠款金额：{{ detailData.arrearsAmount || '-' }}</span>
          </div>
          <h2 class="text-[16px] font-bold mb-[12px] mt-[12px]">房屋建筑物信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">
              房产类型：{{ renderDict(detailData.houseType, 'CT_BASE_ENUM_HouseOwner_HouseType') || '-' }}
            </span>
            <span class="w-[50%]">建筑面积：{{ detailData.structureArea || '-' }}</span>
            <span class="w-[50%]">宗地面积（㎡）：{{ detailData.floorArea || '-' }}</span>
            <span class="w-[50%]">
              建筑结构：{{ renderDict(detailData.buildStructrue, 'CT_BAS_BuildStructrue') || '-' }}
            </span>
            <span class="w-[50%]">建筑年份：{{ detailData.buildYear || '-' }}</span>
            <span class="w-[50%]">层数/总层数：{{ detailData.layerNum || '-' }}</span>
            <span class="w-[50%]">层高（m）：{{ detailData.layerHight || '-' }}</span>
            <span class="w-[50%]">户型：{{ detailData.houseModel || '-' }}</span>
            <span class="w-[50%]">
              消防等级（㎡）：{{ renderDict(detailData.firefightingRate, 'CT_BAS_FirefightingRate') || '-' }}
            </span>
            <span class="w-[50%]">
              房屋安全等级：{{ renderDict(detailData.houseSafeRate, 'CT_BAS_HouseSafeRate') || '-' }}
            </span>
          </div>
        </template>
        <template #taxInfo>
          <h2 class="text-[16px] font-bold mb-[12px]">税务信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">产权比例（%）：{{ detailData.propertyRate || '-' }}</span>
            <span class="w-[50%]">房产税计税原值：{{ detailData.houseTaxOrgValue || '-' }}</span>
            <span class="w-[50%]">增值税率（%）：{{ detailData.addTaxRate || '-' }}</span>
            <span class="w-[50%]">从价月税率（%）：{{ detailData.leaseMonthRate || '-' }}</span>
            <span class="w-[50%]">已租赁面积：{{ detailData.haveLeaseArea || '-' }}</span>
            <span class="w-[50%]">土地面积：{{ detailData.landArea || '-' }}</span>
            <span class="w-[50%]">土地使用税年收费标准：{{ detailData.landUseMonthlyRate || '-' }}</span>
            <span class="w-[50%]">取得日期：{{ detailData.getDate || '-' }}</span>
            <span class="w-[50%]">处置日期：{{ detailData.dealDate || '-' }}</span>
            <span class="w-[50%]">接收日期：{{ detailData.receiveDate || '-' }}</span>
            <span class="w-[50%]">视同销售计提税费：{{ getDicName(detailData.stSaleJTRate, isOrNotDic) || '-' }}</span>
          </div>
        </template>
        <template #fileInfo>
          <h2 class="text-[16px] font-bold mb-[12px]">附件</h2>
          <file-list :biz-id="detailData.id"></file-list>
        </template>
      </anchor-tabs>
    </a-spin>
    <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
  </a-drawer>
</template>
<script setup>
import { isOrNotDic, getDicName } from '@/store/modules/dict.js'
import { renderDict } from '@/utils/render'
import { Modal, message } from 'ant-design-vue'
import { delById, detailById } from '../apis'
import AddEdit from './AddEdit.vue'
import { getFileAccessHttpUrl } from '@/apis/common'
const emits = defineEmits(['loadData'])
const { ids } = defineProps({
  ids: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const curIndex = ref(0)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    const activeIndex = ids.indexOf(data.id)
    curIndex.value = activeIndex === -1 ? 0 : activeIndex
    getDetailById(data.id)
  }
}
defineExpose({ open })

// 通过id获取详情
const getDetailById = async (id) => {
  const { result } = await detailById(id)
  result.ownerCover = getFileAccessHttpUrl(result.ownerCover)
  result.ownerInside = getFileAccessHttpUrl(result.ownerInside)
  result.ownerRealImage = getFileAccessHttpUrl(result.ownerRealImage)
  detailData.value = result
}

// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    AUDITOK: 'success', // 审核通过
    AUDITING: 'processing', // 审核中
    TEMP: 'default', // 暂存
    AUDITNO: 'error', // 审核不通过
    BACK: 'warning', // 已撤回
    ENABLE: 'success', // 启用
    DISABLE: 'error', // 禁用
    CLOSED: 'default', // 关闭
    INTEND: 'blue' // 意向
  }
  return statusMap[status] || 'default'
}

// 导航项
const navList = [
  { name: 'baseInfo', title: '基础信息', showTitle: false },
  { name: 'taxInfo', title: '税务信息', showTitle: false },
  { name: 'fileInfo', title: '附件信息', showTitle: false }
]

const detailData = ref({
  attachmentList: []
})
const loading = ref(false)

const handleSwitch = (type) => {
  // 上一条
  if (type) {
    if (curIndex.value > 0) {
      curIndex.value--
      getDetailById(ids[curIndex.value])
    }
    return
  }
  if (curIndex.value < ids.length - 1) {
    curIndex.value++
    getDetailById(ids[curIndex.value])
  }
}
// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '提示',
    content: '确认删除当前资产？',
    centered: true,
    onOk: async () => {
      await delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      visible.value = false
    }
  })
}
// 页面跳转
const router = useRouter()
const turnToPage = (type) => {
  // 资产处置
  if (type === 1) {
    // 带查询参数
    return router.push({ path: '/assets/disposal', query: { adding: true } })
  }
  // 资产跟踪
  if (type === 2) {
    // 带查询参数
    return router.push({ path: '/assets/track', query: { adding: true } })
  }
}
</script>
