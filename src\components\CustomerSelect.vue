<template>
  <div class="customer-select-input" @click="openModal" @mouseenter="onmouseenter" @mouseleave="onmouseleave">
    <span :placeholder="placeholder">{{ displayValue }}</span>
    <i class="a-icon-close-solid text-[rgba(23,43,82,0.25)]" @click.stop="handleClear" v-if="showClearBtn"></i>
    <i class="a-icon-arrow-down text-[rgba(23,43,82,0.25)]" v-else></i>
  </div>

  <a-modal
    v-model:open="visible"
    :title="title"
    width="800px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form autocomplete="off" layout="inline" class="!mb-[16px]">
      <a-form-item label="客户名称">
        <s-input v-model="params.name" placeholder="搜索客户名称" @input="handleInput"></s-input>
      </a-form-item>
    </a-form>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ y: '50vh' }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getCustomerList } from '@/views/customer/manage/apis'

const { modelValue, multiple } = defineProps({
  modelValue: { required: true, type: [String, Array] },
  placeholder: { type: String, default: '' },
  title: { type: String, default: '选择客户' },
  multiple: { type: Boolean, default: false },
  maxCount: { type: Number, default: undefined }
})

const emit = defineEmits(['update:modelValue', 'change'])

const displayValue = ref('')

const params = reactive({
  name: '',
  status: ''
})

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getCustomerList)
const { selectedRows, selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const columns = [
  { title: '客户名称', dataIndex: 'name', width: 120, fixed: 'left' },
  { title: '客户类型', dataIndex: 'customerType_dictText' },
  { title: '联系电话', dataIndex: 'pushMobile' },
  { title: '管理公司', dataIndex: 'manageCompany_dictText' }
]

const visible = ref(false)

const openModal = () => {
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
  if (Array.isArray(modelValue)) {
    selectedRowKeys.value = modelValue
  } else {
    selectedRowKeys.value = [modelValue]
  }
}

const showClear = ref(false)
const showClearBtn = computed(() => modelValue && modelValue.length > 0 && showClear.value)
const onmouseenter = () => {
  if (modelValue) {
    showClear.value = true
  }
}
const onmouseleave = () => {
  showClear.value = false
}

const handleClear = () => {
  emit('update:modelValue', multiple ? [] : '')
  emit('change', multiple ? [] : '')
  displayValue.value = ''
}

const handleConfirm = () => {
  const value = multiple ? selectedRowKeys.value : selectedRowKeys.value[0]
  displayValue.value = selectedRows.value.map((item) => item.name).join(', ')
  emit('update:modelValue', value)
  emit('change', value)
  handleCancel()
}

const handleCancel = () => {
  visible.value = false
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      pageNo: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

watch(
  () => modelValue,
  async (val) => {
    if (!(val && val.length)) {
      displayValue.value = ''
      return
    }
    if (!selectedRowKeys.value.length) {
      const id = Array.isArray(val) ? val.join(',') : val
      const { result } = await getCustomerList({ id })
      result.records.forEach((item) => {
        selectedRowKeys.value.push(item.id)
        selectedRows.value.push(item)
      })
      displayValue.value = selectedRows.value.map((item) => item.name).join(', ')
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.customer-select-input {
  width: 100%;
  cursor: pointer;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 14px;
  background-color: #fff;
  transition: border-color 0.2s;
  &:hover {
    border-color: var(--color-primary);
  }
  & > span:empty {
    &::after {
      content: attr(placeholder);
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
</style>
