<template>
  <a-modal
    v-model:open="visible"
    class="common-modal"
    title="选择租赁单元"
    width="1200px"
    :mask-closable="false"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <!-- 搜索区域 -->
    <div class="mb-[12px]">
      <a-form layout="inline">
        <a-form-item label="租赁单元名称">
          <a-input v-model:value="searchParams.name" placeholder="请输入租赁单元名称" allow-clear />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button class="ml-2" @click="handleResetSearch">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 表格区域 -->
    <a-table
      class="lease-unit-table"
      :columns="columns"
      :data-source="list"
      :loading="tableLoading"
      :pagination="pagination"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
      row-key="id"
      :scroll="{ x: 1200 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'bizStatus'">
          <a-tag color="success" v-if="record.bizStatus === '在租'">{{ record.bizStatus }}</a-tag>
          <a-tag v-else>{{ record.bizStatus }}</a-tag>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import usePageTable from '@/hooks/usePageTable'

const emit = defineEmits(['confirm'])

const visible = ref(false)
const selectedRowKeys = ref([])
const selectedLeaseUnits = ref([])

// 搜索参数
const searchParams = reactive({
  name: '',
  number: ''
})

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getLeaseUnitList)

const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', ellipsis: true },
  { title: '租赁归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '业务状态', dataIndex: 'bizStatus', width: 100 },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 }
]

const open = (selectedUnits = []) => {
  // 初始化已选择的租赁单元
  if (selectedUnits && selectedUnits.length > 0) {
    selectedLeaseUnits.value = [...selectedUnits]
    selectedRowKeys.value = selectedUnits.map((unit) => unit.id)
  } else {
    selectedLeaseUnits.value = []
    selectedRowKeys.value = []
  }

  // 显示弹窗并加载数据
  visible.value = true
  loadLeaseUnitList()
}

// 加载租赁单元列表
const loadLeaseUnitList = () => {
  onTableFetch({
    ...searchParams,
    pageNo: pagination.value.current,
    pageSize: pagination.value.pageSize
  })

  // 如果有已选择的租赁单元，标记为选中状态
  if (selectedLeaseUnits.value.length > 0) {
    const existingIds = selectedLeaseUnits.value.map((unit) => unit.id)
    // 过滤出当前页面中已选择的租赁单元
    const currentPageSelectedUnits = list.value.filter((unit) => existingIds.includes(unit.id))

    // 更新选中状态
    if (currentPageSelectedUnits.length > 0) {
      const currentPageSelectedIds = currentPageSelectedUnits.map((unit) => unit.id)
      selectedRowKeys.value = [...new Set([...selectedRowKeys.value, ...currentPageSelectedIds])]
    }
  }
}

// 表格选择变化
const onSelectChange = (keys, rows) => {
  selectedRowKeys.value = keys

  // 更新已选择的租赁单元列表
  const newSelectedUnits = [...selectedLeaseUnits.value]

  // 移除取消选择的租赁单元
  selectedLeaseUnits.value.forEach((unit) => {
    if (!keys.includes(unit.id)) {
      const index = newSelectedUnits.findIndex((item) => item.id === unit.id)
      if (index !== -1) {
        newSelectedUnits.splice(index, 1)
      }
    }
  })

  // 添加新选择的租赁单元
  rows.forEach((row) => {
    if (!newSelectedUnits.some((unit) => unit.id === row.id)) {
      newSelectedUnits.push(row)
    }
  })

  selectedLeaseUnits.value = newSelectedUnits
}

// 表格分页变化
const onTableChange = () => {
  loadLeaseUnitList()
}

// 搜索
const handleSearch = () => {
  pagination.value.current = 1
  loadLeaseUnitList()
}

// 重置搜索
const handleResetSearch = () => {
  searchParams.name = ''
  searchParams.number = ''
  pagination.value.current = 1
  loadLeaseUnitList()
}

// 确认选择
const handleOk = () => {
  if (selectedLeaseUnits.value.length === 0) {
    message.warning('请至少选择一个租赁单元')
    return
  }

  emit('confirm', [...selectedLeaseUnits.value])

  visible.value = false
}

// 取消选择
const handleCancel = () => {
  visible.value = false
}

// 暴露方法供父组件调用
defineExpose({ open })
</script>

<style scoped></style>
