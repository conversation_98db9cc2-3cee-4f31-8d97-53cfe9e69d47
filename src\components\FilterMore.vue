更多筛选组件
<template>
  <a-popover
    placement="bottomRight"
    :arrow="false"
    trigger="click"
    overlay-class-name="filter-more-overlay"
    v-model:open="open"
  >
    <template #content>
      <section :style="{ width }">
        <a-form
          autocomplete="off"
          :label-col="{ style: { width: labelWidth } }"
          label-align="left"
          class="max-h-[50vh] overflow-y-auto no-scrollbar"
        >
          <a-form-item :label="item.label" v-for="item in searchList" :key="item.name">
            <s-input
              v-model="paramsTemp[item.name]"
              :show-search-icon="false"
              :placeholder="item.placeholder || ''"
              v-if="item.type === 's-input'"
            ></s-input>
            <a-input
              v-model:value="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              v-else-if="item.type === 'input'"
            ></a-input>
            <dict-select
              v-model="paramsTemp[item.name]"
              :code="item.code"
              :placeholder="item.placeholder || ''"
              v-else-if="item.type === 'dict-select'"
            ></dict-select>
            <a-select
              v-model:value="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :options="item.options"
              show-search
              :filter-option="(input, option) => filterOption(input, option, item)"
              :field-names="item.fieldNames || { label: 'name', value: 'id' }"
              v-else-if="item.type === 'select'"
            ></a-select>
            <api-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="item.asyncFn"
              :field-names="item.fieldNames || { label: 'name', value: 'id' }"
              v-else-if="item.type === 'api-select'"
            ></api-select>
            <api-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="getUserList"
              :field-names="{ label: 'realname', value: 'id' }"
              v-else-if="item.type === 'user-select'"
            ></api-select>
            <api-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="getCustomerList"
              :field-names="{ label: 'name', value: 'id' }"
              v-else-if="item.type === 'customer-select'"
            ></api-select>
            <a-date-picker
              v-model:value="paramsTemp[item.name]"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :placeholder="item.placeholder || ''"
              v-else-if="item.type === 'date'"
            ></a-date-picker>
            <a-tree-select
              v-model:value="paramsTemp[item.name]"
              show-search
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :placeholder="item.placeholder || ''"
              allow-clear
              :tree-data="item.treeData || []"
              :field-names="item.fieldNames || { label: 'name', value: 'id', children: 'children' }"
              :tree-node-filter-prop="item.treeNodeFilterProp || 'name'"
              v-else-if="item.type === 'tree-select'"
            ></a-tree-select>
            <dept-tree-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              v-else-if="item.type === 'dept-tree-select'"
            ></dept-tree-select>
            <dept-tree-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              type="company"
              v-else-if="item.type === 'company-select'"
            ></dept-tree-select>
            <api-tree-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="item.asyncFn"
              :tree-node-filter-prop="item.treeNodeFilterProp || 'name'"
              v-else-if="item.type === 'api-tree-select'"
            ></api-tree-select>
            <a-cascader
              v-model:value="paramsTemp[item.name]"
              :options="item.options"
              :placeholder="item.placeholder || ''"
              :field-names="item.fieldNames || { label: 'name', value: 'id', children: 'children' }"
              v-else-if="item.type === 'cascader'"
            ></a-cascader>
          </a-form-item>
        </a-form>
        <div class="flex items-center justify-between mt-[24px]">
          <span class="text-secondary cursor-pointer transition-colors hover:text-primary" @click="handleReset">
            <i class="a-icon-reset mr-[4px]"></i>
            清空
          </span>
          <div>
            <a-button class="w-[88px]" @click="handleCancel">取消</a-button>
            <a-button class="w-[88px]" type="primary" @click="handleSave">筛选</a-button>
          </div>
        </div>
      </section>
    </template>
    <a-button class="ml-[16px]">
      <i class="a-icon-filter"></i>
      <span>筛选({{ filterCount }})</span>
    </a-button>
  </a-popover>
</template>

<script setup>
import { getUserList } from '@/views/system/user/apis'
import { getCustomerList } from '@/views/customer/manage/apis'

const { params } = defineProps({
  width: { type: String, default: '290px' },
  labelWidth: { type: String, default: '80px' },
  params: { required: true, type: Object },
  searchList: { required: true, type: Array }
})

const emit = defineEmits(['query'])

const open = ref(false)

const paramsTemp = reactive({})
watchEffect(() => {
  for (const key in params) {
    paramsTemp[key] = paramsTemp[key] === undefined ? '' : paramsTemp[key]
  }
})

const handleReset = () => {
  for (const key in paramsTemp) {
    paramsTemp[key] = ''
    params[key] = undefined
  }
  filterCount.value = 0
}
const handleCancel = () => {
  handleReset()
  open.value = false
  emit('query')
}

const filterCount = ref(0)
const handleSave = () => {
  filterCount.value = 0
  for (const key in paramsTemp) {
    if (paramsTemp[key] && paramsTemp[key].length) {
      filterCount.value++
      params[key] = paramsTemp[key]
    } else {
      params[key] = undefined
    }
  }
  open.value = false
  emit('query')
}

const filterOption = (input, option, item) => {
  return option[(item.fieldNames && item.fieldNames.label) || 'name'].toLowerCase().indexOf(input.toLowerCase()) >= 0
}
</script>

<style lang="less">
.filter-more-overlay {
  .ant-popover-inner {
    padding: 24px;
  }
  .ant-form-item {
    margin-bottom: 12px;
  }
}
</style>
