<template>
  <div class="area-manager-selector">
    <a-input
      :value="displayValue"
      :placeholder="placeholder"
      readonly
      :style="style"
      @click="handleClick"
      class="selector-input"
    >
      <template #suffix>
        <i class="a-icon-arrow-down"></i>
        <i v-if="modelValue" class="a-icon-close-solid text-tertiary cursor-pointer" @click.stop="handleClear"></i>
      </template>
    </a-input>

    <a-modal
      v-model:open="visible"
      title="选择片区管理员"
      width="800px"
      class="common-modal"
      :mask-closable="false"
      @ok="handleConfirm"
      @cancel="handleCancel"
    >
      <div v-if="selectedRows.length > 0" class="mb-4 p-2 bg-gray-50 rounded">
        <div class="flex justify-between items-center mb-2">
          <div class="font-bold">已选择</div>
          <a-button type="link" @click="handleClearSelected">清空</a-button>
        </div>
        <div class="flex flex-wrap gap-2">
          <a-tag v-for="row in selectedRows" :key="row.id" closable @close="() => handleRemoveSelected(row.id)">
            {{ row.realname }}
          </a-tag>
        </div>
      </div>
      <!-- 搜索区域 -->
      <div class="mb-4">
        <a-form layout="inline">
          <a-form-item label="部门">
            <dept-tree-select
              class="!w-[200px]"
              v-model="searchParams.company"
              placeholder="请选择部门"
              type="company"
            ></dept-tree-select>
          </a-form-item>
          <a-form-item label="">
            <a-input v-model:value="searchParams.name" allow-clear>
              <template #prefix>
                <i class="a-icon-search text-primary"></i>
              </template>
            </a-input>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button class="ml-2" @click="handleReset">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 表格区域 -->
      <a-table
        :data-source="list"
        :columns="columns"
        :loading="tableLoading"
        :pagination="pagination"
        row-key="id"
        :row-selection="{
          selectedRowKeys: selectedKeys,
          onChange: onSelectChange,
          type: 'checkbox'
        }"
        @change="onTableChange"
      ></a-table>
    </a-modal>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { message } from 'ant-design-vue'
import { getUserList } from '@/views/system/user/apis'

const props = defineProps({
  modelValue: { type: [String, Number, Array], default: () => [] },
  placeholder: { type: String, default: '请选择' },
  style: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['update:modelValue', 'change'])

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getUserList)

// 显示值
const displayValue = ref('')
// 选中的行
const selectedRows = ref([])
// 选中的key
const selectedKeys = ref([])
// 弹窗可见性
const visible = ref(false)

// 搜索参数
const searchParams = reactive({
  name: undefined,
  company: undefined
})

// 表格列定义
const columns = [
  { title: '账号', dataIndex: 'username', width: 200, fixed: 'left' },
  { title: '姓名', dataIndex: 'realname' },
  { title: '性别', dataIndex: 'sex_dictText', width: 80 },
  { title: '手机', dataIndex: 'phone' },
  { title: '部门', dataIndex: 'orgCodeTxt' }
]

/**
 * 表格分页变化处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

/**
 * 点击输入框
 */
const handleClick = () => {
  visible.value = true
  onTableChange()
}

/**
 * 搜索
 */
const handleSearch = () => {
  onTableChange({ pageNo: 1 })
}

/**
 * 重置搜索
 */
const handleReset = () => {
  searchParams.name = ''
  searchParams.company = undefined
  onTableChange({ pageNo: 1 })
}

/**
 * 清除选择
 */
const handleClear = (e) => {
  e.stopPropagation()
  emit('update:modelValue', [])
  emit('change', [])
  displayValue.value = ''
  selectedRows.value = []
  selectedKeys.value = []
}

/**
 * 表格选择变化
 */
const onSelectChange = (keys, rows) => {
  selectedKeys.value = keys
  selectedRows.value = rows
}

/**
 * 移除单个选择
 */
const handleRemoveSelected = (id) => {
  selectedRows.value = selectedRows.value.filter((row) => row.id !== id)
  selectedKeys.value = selectedKeys.value.filter((key) => key !== id)
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length > 0) {
    const ids = selectedRows.value.map((row) => row.id)
    emit('update:modelValue', ids)
    emit('change', ids)
    displayValue.value = selectedRows.value.map((row) => row.realname).join(', ')
    visible.value = false
  } else {
    message.warning('请至少选择一条记录')
  }
}

/**
 * 取消选择
 */
const handleCancel = () => {
  visible.value = false
}

/**
 * 清空已选项
 */
const handleClearSelected = () => {
  selectedRows.value = []
  selectedKeys.value = []
}

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 处理数组类型的值
      const ids = Array.isArray(newVal)
        ? newVal
        : typeof newVal === 'string'
          ? newVal.split(',').filter((id) => id)
          : [newVal]

      // 清空当前选择
      selectedRows.value = []
      selectedKeys.value = []

      // 查找选中的管理员
      if (ids.length > 0) {
        // 先从当前列表中查找
        const managers = ids
          .map((id) => {
            return list.value.find((item) => item.id === id)
          })
          .filter(Boolean)

        if (managers.length > 0) {
          selectedRows.value = managers
          selectedKeys.value = managers.map((m) => m.id)
          displayValue.value = managers.map((m) => m.realname).join(', ')
        } else {
          // 如果找不到对应的管理员信息，显示ID
          displayValue.value = `已选择 ${ids.length} 个管理员`

          // 当列表加载完成后再次尝试查找
          nextTick(() => {
            const foundManagers = ids
              .map((id) => {
                return list.value.find((item) => item.id === id)
              })
              .filter(Boolean)

            if (foundManagers.length > 0) {
              selectedRows.value = foundManagers
              selectedKeys.value = foundManagers.map((m) => m.id)
              displayValue.value = foundManagers.map((m) => m.realname).join(', ')
            }
          })
        }
      }
    } else {
      displayValue.value = ''
      selectedRows.value = []
      selectedKeys.value = []
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.area-manager-selector {
  display: inline-block;
  width: 100%;
}

.selector-input {
  cursor: pointer;
  background-color: #fff;
}

.selector-input :deep(.ant-input) {
  cursor: pointer;
}

.selector-input :deep(.ant-input-suffix) {
  display: flex;
  align-items: center;
}

.selector-input :deep(.a-icon-arrow-down) {
  color: rgba(0, 0, 0, 0.25);
  font-size: 12px;
  margin-right: 6px;
  transition: transform 0.3s;
}

.selector-input:hover :deep(.a-icon-arrow-down) {
  color: #000;
}
</style>
