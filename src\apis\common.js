import request from './http'

/**
 * 全局通用的文件上传接口
 * @param {FormData} formData
 * @param {Object} 非必传，若需要监听进度，则需要传入一个响应式对象，对象中务必包含progress属性，表示进度
 * @param {AbortController} controller 非必传，传了的话可以取消请求
 */
export const uploadFile = (formData, data, controller) => {
  if (data) {
    if (typeof data !== 'object') throw new Error('data 必须是对象类型')
    if (typeof data.progress !== 'string') throw new Error('progress 缺失或不是字符串类型')
  }
  return request({
    method: 'post',
    url: '/sys/common/upload',
    signal: controller?.signal,
    onUploadProgress: (progressEvent) => {
      if (!data) return
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      data.progress = `${percentCompleted}%`
    },
    headers: {
      'Content-Type': 'multipart/form-data;charset=utf-8'
    },
    data: formData
  })
}

/**
 *  获取文件服务访问路径
 * @param fileUrl 文件路径
 * @param prefix(默认http)  文件路径前缀 http/https
 */
export const getFileAccessHttpUrl = (fileUrl, prefix = 'http') => {
  let result = fileUrl
  if (fileUrl && fileUrl.length > 0 && !fileUrl.startsWith(prefix)) {
    // 判断是否是数组格式
    const isArray = fileUrl.indexOf('[') !== -1
    if (!isArray) {
      const prefix = `${import.meta.env.VITE_GLOB_DOMAIN_URL}/sys/common/static/`
      // 判断是否已包含前缀
      if (!fileUrl.startsWith(prefix)) {
        result = `${prefix}${fileUrl}`
      }
    }
  }
  return result
}

/**
 * 高级上传文件功能，可以取消请求，也可以监听上传进度
 * @param {String} url 接口地址
 * @param {Object} data 需要上传的数据，务必包含formData和progress两个属性，formData即原生的FormData，progress用于监听进度
 * @param {AbortController} controller 非必传，传了的话可以取消请求
 */
export const advanceUpload = (url, data, controller) => {
  if (!data || typeof data !== 'object') {
    throw new Error('参数 data 无效')
  }
  const { formData, progress } = data
  if (!(formData instanceof FormData)) {
    throw new Error('formData 缺失或不是 FormData 类型')
  }
  if (typeof progress !== 'string') {
    throw new Error('progress 缺失或不是字符串类型')
  }
  return request({
    method: 'post',
    url,
    signal: controller?.signal,
    onUploadProgress: (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      data.progress = `${percentCompleted}%`
    },
    headers: {
      'Content-Type': 'multipart/form-data;charset=utf-8'
    },
    data: data.formData
  })
}

/**
 *  获取附件
 * @param id 通过业务id获取该业务中上传过的附件id列表
 */
export const getFilesById = async (id) => {
  const { result } = await getAttachmentIdsByBoId(id)
  if (result) {
    const data = await getAttachmentByIds(result)
    data.result.forEach((item) => {
      item.link = getFileAccessHttpUrl(item.filePath)
      item.fileName = `${item.fileName}.${item.fileType}`
    })
    return data.result
  }
  return []
}

// 附件信息-根据单据id查询附件id
export const getAttachmentIdsByBoId = (id) => {
  return request({
    method: 'get',
    url: `/sys/sysAttachment/getAttachmentIdsByBoId?boId=${id}`
  })
}

// 根据附件 ids 查询附件信息
export const getAttachmentByIds = (ids) => {
  return request({
    method: 'get',
    url: `/sys/sysAttachment/queryByIds?ids=${ids}`
  })
}
