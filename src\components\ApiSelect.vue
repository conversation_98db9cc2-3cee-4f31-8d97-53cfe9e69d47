api-select: 需要调用接口获取列表数据的选择器
因ant-design-vue4.x已经使用了虚拟滚动技术，所以对于需要分页的数据，直接使用pageSize:10000来传参即可
<template>
  <a-select
    v-bind="$attrs"
    ref="selectRef"
    show-search
    :value="modelValue || undefined"
    :style="{ width }"
    :filter-option="filterOption"
    :field-names="fieldNames"
    allow-clear
    @change="onchange"
    :options="options"
  ></a-select>
</template>

<script setup>
const { modelValue, asyncFn, fieldNames } = defineProps({
  width: { type: String, default: '100%' },
  modelValue: { required: true, type: [Array, String] },
  fieldNames: { type: Object, default: () => ({ label: 'name', value: 'id' }) },
  /**
   * 注意：如果请求需要传参的话，则在父组件，:async-fn="() => getList({ pageSize: 10000 })"
   * 不能写成:async-fn="getList({ pageSize: 10000 })"
   * 这样的话接收到的async-fn就不是一个函数，而是一个Promise对象，不符合传参要求
   * 如果你的请求不需要传参的话，才可以直接写成:async-fn="getList"这样子
   */
  asyncFn: {
    required: true,
    type: Function,
    validator: (val) => {
      if (typeof val === 'function') return true
      throw new Error('请确认你传入的是一个函数，而不是一个Promise对象')
    }
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const onchange = (val) => {
  emit('update:modelValue', val || '')
  emit('change', val || '')
}

const filterOption = (input, option) => {
  return option[fieldNames.label].toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const options = ref([])

// 获取下拉列表
const getOptions = async (asyncFn) => {
  const { result } = await asyncFn()
  if (result instanceof Array) {
    options.value = result
  } else {
    if (result && result.records) {
      options.value = result.records
    }
  }
}

watch(
  // 当异步函数发生变化，重新执行异步函数，以获取最新数据
  () => asyncFn,
  (fn) => {
    getOptions(fn)
  }
)

onMounted(() => {
  getOptions(asyncFn)
})

defineExpose({
  // 在父组件传递过来的异步函数没有发生变化的情况下，手动提供给父组件，重新执行异步函数的方法
  executeAsyncFn: () => {
    getOptions(asyncFn)
  }
})
</script>
