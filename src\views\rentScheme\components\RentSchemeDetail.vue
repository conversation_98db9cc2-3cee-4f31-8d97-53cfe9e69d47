<template>
  <a-drawer v-model:open="visible" class="common-detail-drawer" placement="right" width="1000px" @close="handleClose">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitch(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitch(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <span class="primary-btn" @click="handleDelete">删除</span>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.title }}</h2>
        <span class="bg-[#edfbe2] rounded-[8px] text-success leading-[28px] px-[8px]">已启用</span>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.updateBy_dictText }} 提交于{{ detail.updateTime }}</span>
      </div>
      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #baseInfo>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">公告标题: {{ detail.title }}</span>
            <span class="w-[50%]">管理公司: {{ detail.title }}</span>
            <span class="w-[50%]">业务日期: {{ detail.bizDate }}</span>
            <span class="w-[50%]">经办人: {{ detail.operator }}</span>
            <span class="w-[50%]">业务部门: {{ detail.title }}</span>
            <span class="w-[50%]">业务状态: {{ detail.title }}</span>
            <span class="w-[50%]">流标次数: {{ detail.title }}</span>
            <span class="w-[50%]">过会文号: {{ detail.reviewDocumentNo }}</span>
            <span class="w-[50%]">批复文号: {{ detail.auditDocumentNo }}</span>
            <span class="w-[50%]">招租方式: {{ detail.rentType_dictText }}</span>
            <span class="w-[50%]">公示开始日期: {{ detail.publicStartTime }}</span>
            <span class="w-[50%]">公示结束日期: {{ detail.publicEndTime }}</span>
            <span class="w-[50%]">招标小组: {{ detail.title }}</span>
            <span class="w-[50%]">招标信息发布日期: {{ detail.publicDate }}</span>
            <span class="w-[50%]">资格审核日期: {{ detail.title }}</span>
            <span class="w-[50%]">中标日期: {{ detail.title }}</span>
          </div>
        </template>
        <template #leaseUnit>
          <a-table
            :data-source="detail.rentSchemeEntryList"
            :columns="columns"
            :pagination="false"
            :scroll="{ x: 1500 }"
          ></a-table>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail, queryLeaseUnit, deleteBatch } from '../apis.js'
import { Modal, message } from 'ant-design-vue'

const { dataList } = defineProps({
  dataList: { required: true, type: Array }
})

const emit = defineEmits(['editRentScheme', 'refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadDetail(id)
  currentIndex.value = dataList.findIndex((item) => item.id === id)
}

const currentIndex = ref(0)
const handleSwitch = (index) => {
  if (!dataList[index]) return
  currentIndex.value = index
  loadDetail(dataList[index].id)
}

const tabList = [
  { title: '基础信息', name: 'baseInfo' },
  { title: '租赁单元', name: 'leaseUnit' },
  { title: '承租方案', name: 'scheme' },
  { title: '委端信息', name: 'info' },
  { title: '竞标客户', name: 'customer' }
]

const loading = ref(false)
const detail = reactive({})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  const { result: list } = await queryLeaseUnit({ id })
  detail.rentSchemeEntryList = list && list.length ? list : []
  loading.value = false
}

const columns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit_dictText', fixed: 'left' },
  { title: '原租金', dataIndex: 'originalRent', width: 200 },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁面积m²', dataIndex: 'leaseArea' },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  { title: '消防等级', dataIndex: 'firefightingRate_dictText' }
]

const handleEdit = () => {
  visible.value = false
  emit('editProject', detail.id)
}
const handleDelete = () => {
  Modal.confirm({
    title: '确认删除该项目？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>
