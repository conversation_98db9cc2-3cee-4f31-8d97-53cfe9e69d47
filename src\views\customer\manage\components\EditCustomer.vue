<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}客户`"
    class="common-drawer"
    placement="right"
    width="1200px"
  >
    <div class="steps"><a-steps :current="currentStep" :items="items"></a-steps></div>

    <a-spin :spinning="confirmLoading">
      <!-- 基础信息及需求 -->
      <div v-show="currentStep === 0">
        <h2 class="text-[16px] font-bold my-[24px]">客户基础信息</h2>
        <a-form ref="basicFormRef" :model="formData" :rules="rules" :label-col="{ span: 4 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="客户名称" name="name" required>
                <a-input v-model:value="formData.name" placeholder="请输入客户名称" show-count :maxlength="50" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否内部公司" name="isInternalCompany">
                <a-checkbox v-model:checked="formData.isInternalCompany">是</a-checkbox>
                <dept-tree-select
                  v-if="formData.isInternalCompany"
                  v-model="formData.internalCompany"
                  placeholder="请选择公司"
                  style="width: 90%"
                  type="company"
                ></dept-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="管理公司" name="manageCompany" required>
                <dept-tree-select
                  v-model="formData.manageCompany"
                  placeholder="请选择管理公司"
                  type="company"
                ></dept-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="客户来源" name="customerSource">
                <dict-select
                  v-model="formData.customerSource"
                  placeholder="客户来源"
                  code="CT_BASE_ENUM_Customer_CustomerSource"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="客户类型" name="customerType">
                <dict-select
                  v-model="formData.customerType"
                  placeholder="客户类型"
                  code="CT_BASE_ENUM_Customer_CustomerType"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="营业执照" name="busiLicence">
                <div class="flex items-center">
                  <a-input v-model:value="formData.busiLicenceNum" placeholder="请输入营业执照号" style="flex: 1" />
                  <div class="ml-2 business-license-upload">
                    <!-- 上传按钮 -->
                    <a-button v-if="!busiLicenceFile.id" @click="handleSelectFile">上传</a-button>

                    <!-- 上传成功状态 -->
                    <div v-if="busiLicenceFile.id" class="flex items-center">
                      <div class="flex items-center bg-gray-50 px-3 py-1 rounded border">
                        <span class="text-sm text-gray-700 max-w-[120px] truncate">{{ busiLicenceFile.name }}</span>
                        <i
                          class="a-icon-refresh ml-2 cursor-pointer text-blue-500 hover:text-blue-700"
                          title="重新上传"
                          @click="handleSelectFile"
                        ></i>
                      </div>
                    </div>

                    <!-- 隐藏的文件输入框 -->
                    <input
                      ref="fileInputRef"
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png,.gif,.bmp"
                      style="display: none"
                      @change="handleFileChange"
                    />
                  </div>
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="法人" name="legalPersonName">
                <a-input v-model:value="formData.legalPersonName" placeholder="请输入法人" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="法人身份证" name="legalPerson">
                <a-input v-model:value="formData.legalPerson" placeholder="请输入法人身份证" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="注册地址" name="registeredAddress">
                <a-cascader
                  v-model:value="formData.registeredPcaCodeArray"
                  placeholder="选择区域"
                  class="pca-code"
                  :options="region"
                  @change="handlePcaCodeChange"
                />
                <a-input
                  v-model:value="formData.registeredAddress"
                  placeholder="请输入详细地址"
                  class="detail-address"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="履约情况" name="performance">
                <dict-select
                  v-model="formData.performance"
                  placeholder="履约情况"
                  code="CT_BAS_Performance"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="安全等级" name="safeRate">
                <dict-select v-model="formData.safeRate" placeholder="安全等级" code="CT_BAS_SafeRate"></dict-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <h2 class="text-[16px] font-bold my-[24px]">客户需求</h2>
        <a-form ref="requirementFormRef" :model="formData" :rules="rules" :label-col="{ span: 4 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="维护日期" name="maintainDate" required>
                <a-date-picker v-model:value="formData.maintainDate" value-format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="维护人员" name="maintainPerson">
                <user-select
                  v-model="formData.maintainPerson"
                  v-model:display-value="formData.maintainPerson_dictText"
                  placeholder="请选择维护人员"
                  title="请选择维护人员"
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="初步需求" name="initRequire" :label-col="{ span: 2 }">
                <a-textarea
                  v-model:value="formData.initRequire"
                  placeholder="请输入初步需求"
                  :rows="4"
                  show-count
                  :maxlength="500"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 联系信息 -->
      <div v-show="currentStep === 1">
        <h2 class="text-[16px] font-bold my-[24px]">联系信息</h2>
        <a-form ref="contactFormRef" :model="formData" :rules="rules" :label-col="{ span: 4 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="联系人" name="linkman">
                <a-input v-model:value="formData.linkman" placeholder="请输入联系人" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="联系地址" name="contactAddress">
                <a-cascader
                  v-model:value="formData.contactRegionArray"
                  placeholder="选择区域"
                  class="pca-code"
                  :options="region"
                  @change="handleContactRegionChange"
                />
                <a-input
                  v-model:value="formData.contactDetailAddress"
                  placeholder="请输入详细地址"
                  class="detail-address"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="联系电话" name="linkmanPhone">
                <a-input v-model:value="formData.linkmanPhone" placeholder="请输入联系电话" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="推送手机" name="pushMobile">
                <a-input v-model:value="formData.pushMobile" placeholder="请输入推送手机" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="邮箱" name="ccEmail">
                <a-input v-model:value="formData.ccEmail" placeholder="请输入邮箱" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 财务信息 -->
      <div v-show="currentStep === 2">
        <h2 class="text-[16px] font-bold my-[24px]">财务信息</h2>
        <a-form ref="financeFormRef" :model="formData" :rules="rules" :label-col="{ span: 4 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="开户行" name="depositBank">
                <a-input v-model:value="formData.depositBank" placeholder="请输入开户行" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="开户行账号" name="depositBankAccount">
                <a-input v-model:value="formData.depositBankAccount" placeholder="请输入开户行账号" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="发票名称" name="invoiceName">
                <a-input v-model:value="formData.invoiceName" placeholder="请输入发票名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="发票类型" name="invoiceType">
                <dict-select
                  v-model="formData.invoiceType"
                  placeholder="发票类型"
                  code="CT_BASE_ENUM_Customer_InvoiceType"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="税率(%)" name="taxRate">
                <a-input-number v-model:value="formData.taxRate" style="width: 100%" addon-after="%" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">完成并启用</a-button>
      <a-button :disabled="currentStep <= 0" @click="handlePrevStep">上一步</a-button>
      <a-button :disabled="currentStep >= items.length - 1" @click="handleNextStep">下一步</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import region from '@/json/region.json'
import { addCustomer, updateCustomer } from '../apis'
import { uploadFile } from '@/apis/common'

const emit = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const currentStep = ref(0)

const basicFormRef = ref()
const requirementFormRef = ref()
const contactFormRef = ref()
const financeFormRef = ref()

// 营业执照上传相关
const fileInputRef = ref()
const busiLicenceFile = reactive({
  id: '',
  name: ''
})

const items = [{ title: '基础信息及需求' }, { title: '联系信息' }, { title: '财务信息' }]

const rules = {
  // 基础信息
  name: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '客户名称长度应为2-50个字符', trigger: 'blur' }
  ],
  manageCompany: [{ required: true, message: '请选择管理公司', trigger: 'change' }],
  maintainDate: [{ required: true, message: '请选择维护日期', trigger: 'change' }],

  // 联系信息
  linkmanPhone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  pushMobile: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  ccEmail: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }],

  // 财务信息
  taxRate: [{ type: 'number', min: 0, message: '税率不能为负数', trigger: 'blur' }]
}

// 表单默认值
const formDataDefault = reactive({
  // ID和编号字段
  id: '',
  number: '',

  // 基本信息字段
  name: '',
  isInternalCompany: false,
  internalCompany: '',
  region: '',
  address: '',
  customerType: '',
  busiLicence: '',
  busiLicenceNum: '',
  legalPerson: '',
  legalPersonName: '',
  manageCompany: '',
  customerSource: '',
  performance: '',
  safeRate: '',

  // 地址相关字段
  registeredPcaCode: '',
  registeredPcaCodeArray: [],
  registeredAddress: '',

  // 联系信息
  linkman: '',
  linkmanPhone: '',
  contactAddress: '',
  contactRegion: [],
  contactDetailAddress: '',
  pushMobile: '',
  ccEmail: '',

  // 财务信息
  depositBank: '',
  depositBankAccount: '',
  invoiceName: '',
  invoiceType: '',
  taxRate: null,

  // 需求信息
  maintainDate: null,
  maintainPerson: '',
  initRequire: ''
})
const formData = reactive({ ...formDataDefault })

/**
 * 打开编辑抽屉
 */
const open = (data) => {
  if (data && data.id) {
    Object.assign(formData, data)

    // 如果有 registeredPcaCode，则拆分为数组
    if (formData.registeredPcaCode && typeof formData.registeredPcaCode === 'string') {
      formData.registeredPcaCodeArray = formData.registeredPcaCode.split(',').filter((code) => code)
    }

    // 如果有 contactRegion，则拆分为数组
    if (formData.contactRegion && typeof formData.contactRegion === 'string') {
      formData.contactRegionArray = formData.contactRegion.split(',').filter((code) => code)
    }

    // 将字符串类型的 isInternalCompany 转换为布尔类型
    if (typeof formData.isInternalCompany === 'string') {
      formData.isInternalCompany = formData.isInternalCompany === 'true' || formData.isInternalCompany === '1'
    }

    // 如果有营业执照文件，设置文件状态
    if (formData.busiLicence) {
      busiLicenceFile.id = formData.busiLicence
      busiLicenceFile.name = '营业执照文件' // 这里可以根据实际需求获取真实文件名
    }
  }

  visible.value = true
}

const handleNextStep = async () => {
  if (currentStep.value >= items.length - 1) return
  try {
    switch (currentStep.value) {
      case 0:
        await basicFormRef.value?.validate()
        await requirementFormRef.value?.validate()
        break
      case 1:
        await contactFormRef.value?.validate()
        break
    }
    currentStep.value++
  } catch {
    message.error('请填写完必填项后再进入下一步')
  }
}

const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

/**
 * 处理省市区选择变化
 */
const handlePcaCodeChange = (value) => {
  formData.registeredPcaCode = value.join(',')
  formData.registeredPcaCodeArray = value
}

/**
 * 处理联系地址选择变化
 */
const handleContactRegionChange = (value) => {
  formData.contactRegion = value.join(',')
  formData.contactRegionArray = value
}

/**
 * 点击选择文件
 */
const handleSelectFile = () => {
  fileInputRef.value?.click()
}

/**
 * 文件选择变化处理
 */
const handleFileChange = async (event) => {
  const files = event.target.files
  if (!files || files.length === 0) return

  const file = files[0]

  // 文件类型验证
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp']
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持上传 PDF 或图片格式文件（jpg、jpeg、png、gif、bmp）')
    event.target.value = ''
    return
  }

  // 文件大小验证（限制5MB）
  const maxSize = 5 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过 5MB')
    event.target.value = ''
    return
  }

  // 开始上传
  await uploadBusiLicenceFile(file)
  event.target.value = ''
}

/**
 * 上传营业执照文件
 */
const uploadBusiLicenceFile = async (file) => {
  try {
    busiLicenceFile.name = file.name

    // 设置整个抽屉为加载状态
    confirmLoading.value = true

    // 创建 FormData
    const uploadFormData = new FormData()
    uploadFormData.append('file', file)

    // 上传文件
    const response = await uploadFile(uploadFormData)

    // 上传成功 - 检查返回的数据结构
    if (response && response.message) {
      busiLicenceFile.id = response.message
      formData.busiLicence = response.message
      message.success('营业执照上传成功')
    } else {
      throw new Error('上传响应格式错误')
    }
  } catch {
    busiLicenceFile.id = ''
    busiLicenceFile.name = ''
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 保存客户信息
 */
const handleSave = async () => {
  confirmLoading.value = true

  try {
    // 提交表单数据
    if (formData.id) {
      await updateCustomer(formData)
      message.success('客户信息更新成功')
    } else {
      formData.status = '1' // 新建客户默认启用
      await addCustomer(formData)
      message.success('客户添加成功')
    }
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 暂存客户信息
 */
const handleTemporaryStorage = async () => {
  confirmLoading.value = true

  try {
    if (formData.id) {
      await updateCustomer(formData)
      message.success('客户信息更新成功')
    } else {
      await addCustomer(formData)
      message.success('客户暂存成功')
    }
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 取消编辑并重置表单
 */
const handleCancel = () => {
  visible.value = false
  currentStep.value = 0
  Object.assign(formData, formDataDefault)

  // 重置营业执照文件状态
  busiLicenceFile.id = ''
  busiLicenceFile.name = ''

  basicFormRef.value?.resetFields()
  requirementFormRef.value?.resetFields()
  contactFormRef.value?.resetFields()
  emit('refresh')
}

defineExpose({ open })
</script>

<style scoped>
.pca-code,
.detail-address {
  width: 50%;
}

.pca-code :deep(.ant-select-selector) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
}
.detail-address {
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.steps {
  margin-bottom: 20px;
}

.business-license-upload {
  min-width: 120px;
}

.business-license-upload .a-icon-refresh:hover {
  cursor: pointer;
}
</style>
