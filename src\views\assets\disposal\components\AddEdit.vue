<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="ruleForm.id ? '编辑资产处置' : '新增资产处置'"
    placement="right"
    width="1200px"
    @close="handleCancel"
  >
    <div class="progress-wrapper mb-[24px]">
      <div class="progress-item mr-[16px]" v-for="(item, index) in processList" :key="index">
        <span class="item-step" :class="{ active: item.status }">
          <span v-if="item.status === 2" class="a-icon-dagou"></span>
          <span v-else class="step-num flex items-center">{{ index + 1 }}</span>
        </span>
        <span class="item-name" :class="{ active: item.status }">{{ item.name }}</span>
        <span class="item-line" :class="{ active: item.status === 2 }"></span>
      </div>
    </div>

    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '150px' } }"
      autocomplete="off"
    >
      <!-- 基础信息 -->
      <template v-if="processList[0].status == 1">
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">资产基础信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="管理公司" name="manageCompany">
              <dept-tree-select
                v-model="ruleForm.manageCompany"
                placeholder="请选择管理公司"
                @change="manageCompanyChange"
              ></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="业务日期" name="bizDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.bizDate"
                type="date"
                placeholder="请选择业务日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="资产来源" name="assetsSource">
              <dict-select
                v-model="ruleForm.assetsSource"
                placeholder="请选择资产来源"
                code="CT_BAS_AssetsSource"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="处置说明" name="dealExplain">
              <a-input
                v-model:value="ruleForm.dealExplain"
                placeholder="请输入处置说明"
                :maxlength="200"
                show-count
              ></a-input>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="房产处置日期" name="houseDealDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.houseDealDate"
                type="date"
                placeholder="请选择房产处置日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="转让方式" name="transferMethod">
              <div>
                <dict-select
                  v-model="ruleForm.transferMethod"
                  placeholder="请选择资产类型"
                  code="CT_BAS_TransferMethod"
                  style="width: calc(100% - 100px)"
                ></dict-select>
                <a-checkbox class="!ml-[10px]" v-model:checked="ruleForm.isUnion" style="width: 90px">
                  联合转让
                </a-checkbox>
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="转让方" name="transferor">
              <dept-tree-select v-model="ruleForm.transferor" placeholder="请选择转让方"></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="内部决策情况" name="decision">
              <dict-select
                v-model="ruleForm.decision"
                placeholder="请选择内部决策情况"
                code="CT_BASE_ENUM_HouseDealBill_Decision"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="披露内容描述" name="discloseDesc">
              <a-textarea
                v-model:value="ruleForm.discloseDesc"
                placeholder="请输入披露内容描述"
                :maxlength="500"
                show-count
              />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="mb-[12px] mt-[12px] flex justify-between">
          <div class="text-[16px] font-bold">处置资产</div>
          <a-button type="primary" ghost @click="handleAddHouseDealBillEntryList">
            <span class="a-icon-plus mr-[8px]"></span>
            增加记录
          </a-button>
        </div>
        <a-table
          :data-source="ruleForm.houseDealBillEntryList"
          :columns="columns"
          :scroll="{ y: 300, x: 1500 }"
          :pagination="false"
        >
          <template #headerCell="{ column }">
            <template v-if="column.dataIndex === 'transferArea'">
              <span class="table-header-col">{{ column.title }}</span>
            </template>
            <template v-if="column.dataIndex === 'evaluatePrice'">
              <span class="table-header-col">{{ column.title }}</span>
            </template>
            <template v-if="column.dataIndex === 'transferee'">
              <span class="table-header-col">{{ column.title }}</span>
            </template>
            <template v-if="column.dataIndex === 'dealExplain'">
              <span class="table-header-col">{{ column.title }}</span>
            </template>
            <template v-if="column.dataIndex === 'deal'">
              <span class="table-header-col">{{ column.title }}</span>
            </template>
            <template v-if="column.dataIndex === 'dealArea'">
              <span class="table-header-col">{{ column.title }}</span>
            </template>
            <template v-if="column.dataIndex === 'dealPrice'">
              <span class="table-header-col">{{ column.title }}</span>
            </template>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'houseOwner'">
              <div class="mb-[24px]">
                <span class="a-icon-remove remove-btn cursor-pointer font-18 mr-[5px]"></span>
                <span>{{ record.houseOwner }}</span>
              </div>
            </template>

            <template v-if="column.dataIndex === 'transferArea'">
              <a-form-item
                label=""
                :name="['houseDealBillEntryList', index, 'transferArea']"
                :rules="{
                  required: true,
                  message: '请输入转让面积',
                  trigger: 'change'
                }"
              >
                <a-input-number
                  v-model:value="record.transferArea"
                  class="!w-[100%]"
                  :min="0"
                  :precision="2"
                  placeholder="请输入转让面积"
                ></a-input-number>
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'evaluatePrice'">
              <a-form-item
                label=""
                :name="['houseDealBillEntryList', index, 'evaluatePrice']"
                :rules="{
                  required: true,
                  message: '请输入评估价值',
                  trigger: 'change'
                }"
              >
                <a-input-number
                  v-model:value="record.evaluatePrice"
                  class="!w-[100%]"
                  :min="0"
                  :precision="2"
                  placeholder="请输入评估价值"
                ></a-input-number>
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'transferee'">
              <a-form-item
                label=""
                :name="['houseDealBillEntryList', index, 'transferee']"
                :rules="{
                  required: true,
                  message: '请输入受让方',
                  trigger: 'change'
                }"
              >
                <a-input v-model:value="record.transferee" placeholder="请输入受让方" class="w-[100%]"></a-input>
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'dealExplain'">
              <a-form-item
                label=""
                :name="['houseDealBillEntryList', index, 'dealExplain']"
                :rules="{
                  required: true,
                  message: '请输入处置说明',
                  trigger: 'change'
                }"
              >
                <a-input v-model:value="record.dealExplain" placeholder="请输入处置说明" class="w-[100%]"></a-input>
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'deal'">
              <a-form-item
                label=""
                :name="['houseDealBillEntryList', index, 'deal']"
                :rules="{
                  required: false,
                  message: '请勾选是否成交',
                  trigger: 'change'
                }"
              >
                <a-checkbox v-model:checked="record.deal">成交</a-checkbox>
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'dealArea'">
              <a-form-item
                label=""
                :name="['houseDealBillEntryList', index, 'dealArea']"
                :rules="{
                  required: false,
                  message: '请输入成交面积',
                  trigger: 'blur'
                }"
              >
                <a-input-number
                  v-model:value="record.dealArea"
                  :min="0"
                  :precision="2"
                  class="!w-[100%]"
                  placeholder="请输入成交面积"
                />
              </a-form-item>
            </template>

            <template v-if="column.dataIndex === 'dealPrice'">
              <a-form-item
                label=""
                :name="['houseDealBillEntryList', index, 'dealPrice']"
                :rules="{
                  required: false,
                  message: '请输入成交价',
                  trigger: 'blur'
                }"
              >
                <a-input-number
                  v-model:value="record.dealPrice"
                  :min="0"
                  :precision="2"
                  class="!w-[100%]"
                  placeholder="请输入成交价"
                />
              </a-form-item>
            </template>

            <template v-else-if="column.dataIndex === 'action'">
              <a-button class="!mb-[24px]" type="primary" link>资产详情</a-button>
            </template>
          </template>
        </a-table>
      </template>

      <!-- 处置过程 -->
      <template v-if="processList[1].status == 1">
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">集团批复</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="请示文号" name="reqReferNum">
              <a-input v-model:value="ruleForm.reqReferNum" placeholder="请输入请示文号" class="w-[100%]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="批复文号" name="replyReferNum">
              <a-input v-model:value="ruleForm.replyReferNum" placeholder="请输入批复文号" class="w-[100%]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="批复日期" name="replyDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.replyDate"
                type="date"
                placeholder="请选择批复日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">评估正式稿</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="评估报告编号" name="assessReportNum">
              <a-input v-model:value="ruleForm.assessReportNum" placeholder="请输入评估报告编号" class="w-[100%]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="评估机构名称" name="assessOrg">
              <a-input v-model:value="ruleForm.assessOrg" placeholder="请输入评估机构名称" class="w-[100%]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="评估方式" name="assessWayBase">
              <dict-select
                v-model="ruleForm.assessWayBase"
                placeholder="请选择评估方式"
                code="CT_BAS_AssessWayBase"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="评估师编号" name="appraiserNum">
              <a-input v-model:value="ruleForm.appraiserNum" placeholder="请输入评估师编号" class="w-[100%]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="评估师名称" name="apprasierName">
              <a-input v-model:value="ruleForm.apprasierName" placeholder="请输入评估师名称" class="w-[100%]" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="评估价值(万元)" name="assessVal">
              <a-input-number
                v-model:value="ruleForm.assessVal"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入评估价值"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="评估基准日" name="assessStandardDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.assessStandardDate"
                type="date"
                placeholder="请选择评估基准日"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="账面价值(万元)" name="paperVal">
              <a-input-number
                v-model:value="ruleForm.paperVal"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入账面价值"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="增减值(万元)" name="addSubVal">
              <a-input-number
                v-model:value="ruleForm.addSubVal"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入增减值"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="增值率(%)" name="addRate">
              <a-input-number
                v-model:value="ruleForm.addRate"
                addon-after="%"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入账面价值"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="评估正式稿出具日期" name="assessReportDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.assessReportDate"
                type="date"
                placeholder="请选择评估正式稿出具日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">产权交易中心挂牌</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="转让底价(万元)" name="transferBaseVal">
              <a-input-number
                v-model:value="ruleForm.transferBaseVal"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入转让底价"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="保证金(万元)" name="assureCash">
              <a-input-number
                v-model:value="ruleForm.assureCash"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入保证金"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="最高加价幅度(%)" name="maxUpRange">
              <a-input-number
                v-model:value="ruleForm.maxUpRange"
                addon-after="%"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入最高加价幅度"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承租人是否行使优先权" name="renterIsUsePriority">
              <a-select
                v-model:value="ruleForm.renterIsUsePriority"
                placeholder="请选择承租人是否行使优先权"
                allow-clear
              >
                <a-select-option v-for="item in isOrNotDic" :key="item.dictKey" :value="item.dictKey">
                  {{ item.dictValue }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">拍卖行成交确认书</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="成交价(万元)" name="dealCloseVal">
              <a-input-number
                v-model:value="ruleForm.dealCloseVal"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入成交价"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="成交人名称" name="dealCloseName">
              <a-input v-model:value="ruleForm.dealCloseName" placeholder="请输入成交人名称" class="w-[100%]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="身份证号" name="dealCloseIdCardNum">
              <a-input v-model:value="ruleForm.dealCloseIdCardNum" placeholder="请输入身份证号" class="w-[100%]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="成交人电话" name="dealClosePhone">
              <a-input v-model:value="ruleForm.dealClosePhone" placeholder="请输入成交人电话" class="w-[100%]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="成交日期" name="dealCloseDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.dealCloseDate"
                type="date"
                placeholder="请选择成交日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">产权交易中心鉴证书</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="出具日期" name="provideAuthBookDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.provideAuthBookDate"
                type="date"
                placeholder="请选择出具日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">移交房产</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="移交日期" name="turnHouseDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.turnHouseDate"
                type="date"
                placeholder="请选择移交日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">划拨转出让</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="划拨转出让日期" name="transferOutDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.transferOutDate"
                type="date"
                placeholder="请选择划拨转出让日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">网签过户</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="网签过户日期" name="netSignDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.netSignDate"
                type="date"
                placeholder="请选择网签过户日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </template>

      <!-- 资产评估 -->
      <template v-if="processList[2].status == 1">
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">税务信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="评估机构" name="assessOrganizati">
              <dict-select
                v-model="ruleForm.assessOrganizati"
                placeholder="请选择评估机构"
                code="CT_BAS_AssessOrganization"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="评估核准(备案)机构" name="approveOrganizat">
              <dict-select
                v-model="ruleForm.approveOrganizat"
                placeholder="请选择评估核准(备案)机构"
                code="CT_BAS_AssessOrganization"
              ></dict-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="核准(备案)日期" name="approveDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.approveDate"
                placeholder="请选择核准(备案)日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="评估基准日" name="assessDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.assessDate"
                placeholder="请选择评估基准日"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="评估报告文号(万元)" name="assessReportNo">
              <a-input-number
                v-model:value="ruleForm.assessReportNo"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入评估报告文号"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="评估基准日审计机构" name="auditOrganizatio">
              <dict-select
                v-model="ruleForm.auditOrganizatio"
                placeholder="请选择评估基准日审计机构"
                code="CT_BAS_AssessOrganization"
              ></dict-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="律师事务所" name="lawOffice">
              <dict-select
                v-model="ruleForm.lawOffice"
                placeholder="请选择律师事务所"
                code="CT_BASE_ENUM_HouseDealBill_LawOffice"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="标的评估值" name="appraisePrice">
              <a-input-number
                v-model:value="ruleForm.appraisePrice"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入标的评估值"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="转让标的评估总值" name="appraiseTotalPrice">
              <a-input-number
                v-model:value="ruleForm.appraiseTotalPrice"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入转让标的评估总值"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </template>

      <!-- 披露信息 -->
      <template v-if="processList[3].status == 1">
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">披露信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="公示开始日期" name="publicityStartDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.publicityStartDate"
                placeholder="请选择公示开始日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="公示截止日期" name="publicityEndDate">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.publicityEndDate"
                placeholder="请选择公示截止日期"
                class="w-[100%]"
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="内容描述" name="contentDesc">
              <a-textarea
                v-model:value="ruleForm.contentDesc"
                placeholder="请输入内容描述"
                :maxlength="500"
                show-count
              />
            </a-form-item>
          </a-col>
        </a-row>
      </template>
    </a-form>

    <template #footer>
      <a-button type="primary" @click="handleConfirm" :loading="submitLoading">
        <span class="a-icon-dagou mr-[8px]"></span>
        提交
      </a-button>
      <a-button v-if="curIndex" type="primary" ghost @click="handlePrev">上一步</a-button>
      <a-button v-if="curIndex !== 3" type="primary" ghost @click="handleNext">下一步</a-button>
      <a-button type="primary" ghost @click="handleConfirm(1)" :loading="stashLoading">提交并继续添加</a-button>
      <a-button type="primary" ghost @click="handleStash" :loading="stashLoading">暂存</a-button>
      <!-- <a-button @click="handleCancel">取消</a-button> -->
    </template>
  </a-drawer>
</template>
<script setup>
import { message } from 'ant-design-vue'
// import { projectPage } from '@/views/projects/apis.js'
import { isOrNotDic } from '@/store/modules/dict.js'
import { stash, submit, edit } from '../apis'
const emits = defineEmits(['loadData'])
const curIndex = ref(0)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    Object.assign(ruleForm, data)
  }
}
defineExpose({ open })

const processList = reactive([
  { name: '基础信息', status: 1 },
  { name: '处置过程', status: 0 },
  { name: '资产评估', status: 0 },
  { name: '披露信息', status: 0 }
])

const ruleForm = reactive({
  id: '',
  status: '',
  number: '',
  manageCompany: '',
  bizDate: '',
  assetsSource: '',
  dealExplain: '',
  houseDealDate: '',
  transferMethod: '',
  isUnion: false,
  transferor: '',
  decision: '',
  discloseDesc: '',
  reqReferNum: '',
  replyReferNum: '',
  replyDate: '',
  assessReportNum: '',
  assessOrg: '',
  assessWayBase: '',
  appraiserNum: '',
  apprasierName: '',
  assessVal: '',
  assessStandardDate: '',
  paperVal: '',
  addSubVal: '',
  addSubRate: '',
  addRate: '',
  assessReportDate: '',
  transferBaseVal: '',
  assureCash: '',
  maxUpRange: '',
  renterIsUsePriority: '',
  dealCloseVal: '',
  dealCloseName: '',
  dealCloseIdCardNum: '',
  dealClosePhone: '',
  dealCloseDate: '',
  provideAuthBookDate: '',
  turnHouseDate: '',
  transferOutDate: '',
  netSignDate: '',
  assessOrganizati: '',
  approveOrganizat: '',
  approveDate: '',
  assessDate: '',
  assessReportNo: '',
  auditOrganizatio: '',
  lawOffice: '',
  appraisePrice: '',
  appraiseTotalPrice: '',
  publicityStartDate: '',
  publicityEndDate: '',
  contentDesc: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  auditBy: '',
  auditTime: '',
  attachmentIds: '',
  sourceBillId: '',
  sourceBillEntryId: '',
  houseDealBillEntryList: []
})

const rules = computed(() => ({
  manageCompany: [{ required: [0, 3].includes(curIndex.value), message: '请选择管理公司', trigger: ['blur'] }],
  bizDate: [{ required: [0, 3].includes(curIndex.value), message: '请选择业务日期', trigger: ['blur'] }],
  assetsSource: [{ required: [0, 3].includes(curIndex.value), message: '请选择资产来源', trigger: ['blur'] }],
  dealExplain: [{ required: [0, 3].includes(curIndex.value), message: '请输入处置说明', trigger: ['blur'] }],
  houseDealDate: [{ required: [0, 3].includes(curIndex.value), message: '请选择房产处置日期', trigger: ['blur'] }],
  transferMethod: [{ required: [0, 3].includes(curIndex.value), message: '请选择转让方式', trigger: ['blur'] }],
  transferor: [{ required: [0, 3].includes(curIndex.value), message: '请选择转让方', trigger: ['blur'] }],
  discloseDesc: [{ required: [0, 3].includes(curIndex.value), message: '请输入披露内容描述', trigger: ['blur'] }]
}))

const selectCompanyInfo = ref({
  companyName: '',
  address: ''
})
// 选择管理公司的回调
const manageCompanyChange = () => {
  // ruleForm.houseDealBillEntryList = []
  // selectCompanyInfo.value.companyName = node.departName
  // selectCompanyInfo.value.address = node.address
}

const columns = [
  { title: '资产名称', dataIndex: 'houseOwner', width: 150, fixed: true },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '转让面积(㎡)', dataIndex: 'transferArea' },
  { title: '评估价值(万元)', dataIndex: 'evaluatePrice' },
  { title: '受让方', dataIndex: 'transferee' },
  { title: '处置说明', dataIndex: 'dealExplain' },
  { title: '是否成交', dataIndex: 'deal' },
  { title: '成交面积', dataIndex: 'dealArea' },
  { title: '成交价（万元）', dataIndex: 'dealPrice' },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]

const handleAddHouseDealBillEntryList = () => {
  if (!ruleForm.manageCompany) {
    return message.warning('请先选择管理公司')
  }
  ruleForm.houseDealBillEntryList.push({
    // seq: 0,
    // delFlag: 0
    id: '',
    houseOwner: selectCompanyInfo.value.companyName,
    detailAddress: selectCompanyInfo.value.address,
    transferArea: '',
    evaluatePrice: '',
    transferee: '',
    dealExplain: '',
    deal: false,
    parent: '',
    sourceBillId: '',
    sourceBillEntryId: ''
  })
}

const formRef = ref()
const submitLoading = ref(false)
// 提交
const handleConfirm = async (type) => {
  await formRef.value.validate()
  submitLoading.value = true
  try {
    await submit(ruleForm)
    message.success('提交成功')
    if (type) {
      return clearForm()
    }
    visible.value = false
  } finally {
    submitLoading.value = false
  }
}
// 上一步
const handlePrev = () => {
  processList[curIndex.value].status = 0
  processList[curIndex.value - 1].status = 1
  curIndex.value--
}
// 下一步
const handleNext = async () => {
  await formRef.value.validate()
  processList[curIndex.value].status = 2
  processList[curIndex.value + 1].status = 1
  curIndex.value++
}
// 暂存
const stashLoading = ref(false)
const handleStash = async () => {
  await formRef.value.validate()
  stashLoading.value = true
  try {
    await (ruleForm.id ? edit(ruleForm) : stash(ruleForm))
    message.success('保存成功')
    emits('loadData')
    stashLoading.value = false
  } finally {
    stashLoading.value = false
  }
}

const clearForm = () => {
  ruleForm.id = ''
  ruleForm.status = ''
  ruleForm.number = ''
  ruleForm.manageCompany = ''
  ruleForm.bizDate = ''
  ruleForm.assetsSource = ''
  ruleForm.dealExplain = ''
  ruleForm.houseDealDate = ''
  ruleForm.transferMethod = ''
  ruleForm.isUnion = false
  ruleForm.transferor = ''
  ruleForm.decision = ''
  ruleForm.discloseDesc = ''
  ruleForm.reqReferNum = ''
  ruleForm.replyReferNum = ''
  ruleForm.replyDate = ''
  ruleForm.assessReportNum = ''
  ruleForm.assessOrg = ''
  ruleForm.assessWayBase = ''
  ruleForm.appraiserNum = ''
  ruleForm.apprasierName = ''
  ruleForm.assessVal = ''
  ruleForm.assessStandardDate = ''
  ruleForm.paperVal = ''
  ruleForm.addSubVal = ''
  ruleForm.addSubRate = ''
  ruleForm.addRate = ''
  ruleForm.assessReportDate = ''
  ruleForm.transferBaseVal = ''
  ruleForm.assureCash = ''
  ruleForm.maxUpRange = ''
  ruleForm.renterIsUsePriority = ''
  ruleForm.dealCloseVal = ''
  ruleForm.dealCloseName = ''
  ruleForm.dealCloseIdCardNum = ''
  ruleForm.dealClosePhone = ''
  ruleForm.dealCloseDate = ''
  ruleForm.provideAuthBookDate = ''
  ruleForm.turnHouseDate = ''
  ruleForm.transferOutDate = ''
  ruleForm.netSignDate = ''
  ruleForm.assessOrganizati = ''
  ruleForm.approveOrganizat = ''
  ruleForm.approveDate = ''
  ruleForm.assessDate = ''
  ruleForm.assessReportNo = ''
  ruleForm.auditOrganizatio = ''
  ruleForm.lawOffice = ''
  ruleForm.appraisePrice = ''
  ruleForm.appraiseTotalPrice = ''
  ruleForm.publicityStartDate = ''
  ruleForm.publicityEndDate = ''
  ruleForm.contentDesc = ''
  ruleForm.remark = ''
  ruleForm.createBy = ''
  ruleForm.createTime = ''
  ruleForm.updateBy = ''
  ruleForm.updateTime = ''
  ruleForm.auditBy = ''
  ruleForm.auditTime = ''
  ruleForm.attachmentIds = ''
  ruleForm.sourceBillId = ''
  ruleForm.sourceBillEntryId = ''
  ruleForm.houseDealBillEntryList = []
  formRef.value.clearValidate()
}
// 取消
const handleCancel = () => {
  clearForm()
  visible.value = false
}
</script>

<style scoped lang="less">
.progress-wrapper {
  width: 100%;
  display: flex;
  align-items: center;

  .progress-item {
    flex-grow: 1;
    display: flex;
    align-items: center;
    white-space: nowrap;
    gap: 12px;
    .item-step {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      display: inline-block;
      border: 1px solid #8992a3;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      &.active {
        background: #165dff;
        .step-num {
          color: #fff;
        }
      }
      .a-icon-dagou {
        color: #fff;
      }
      .step-num {
        color: #8992a3;
      }
    }
    .item-name {
      flex-shrink: 0;
      color: #8992a3;
      &.active {
        color: #1d335c;
      }
    }
    .item-line {
      flex-grow: 1;
      padding: 1px;
      display: inline-block;
      // min-width: 200px;
      background: #d7dae0;
      &.active {
        background: #165dff;
      }
    }
  }
  .progress-item:last-child {
    .item-line {
      display: none;
    }
  }
}
// 自定义表头文本必填
.table-header-col {
  &::after {
    display: inline-block;
    content: '*';
    color: var(--color-red-600);
  }
}
// 移除按钮hover样式
.remove-btn {
  font-size: 16px;
  &:hover {
    color: var(--color-red-600);
  }
}
</style>
