<template>
  <div
    class="contract-select-input"
    :style="{ width }"
    @click="openModal"
    @mouseenter="onmouseenter"
    @mouseleave="onmouseleave"
  >
    <span :placeholder="placeholder">{{ displayValue }}</span>
    <i class="a-icon-close-solid text-[rgba(23,43,82,0.25)]" @click.stop="handleClear" v-if="showClearBtn"></i>
    <i class="a-icon-arrow-down text-[rgba(23,43,82,0.25)]" v-else></i>
  </div>

  <a-modal
    v-model:open="visible"
    :title="title"
    width="800px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form autocomplete="off" layout="inline" class="!mb-[16px]">
      <a-form-item label="合同编号">
        <s-input v-model="params.contractNumber" placeholder="搜索合同编号" @input="handleInput"></s-input>
      </a-form-item>
      <a-form-item>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
      </a-form-item>
    </a-form>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ y: '50vh' }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page } from '@/views/contract/management/apis'
import { renderDictTag } from '@/utils/render'

const { modelValue, multiple } = defineProps({
  modelValue: { required: true, type: [String, Array] },
  placeholder: { type: String, default: '' },
  title: { type: String, default: '选择合同' },
  multiple: { type: Boolean, default: false },
  maxCount: { type: Number, default: undefined },
  width: { type: String, default: '100%' }
})

const emit = defineEmits(['update:modelValue', 'change'])

const displayValue = ref('')

const params = reactive({
  contractNumber: '',
  status: 'AUDITOK'
})

const { list, pagination, tableLoading, onTableFetch } = usePageTable(page)
const { selectedRows, selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const columns = [
  { title: '合同编号', dataIndex: 'contractNumber', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 120 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_Contract_BizStatus', 'dot')
  },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160 },
  { title: '业务日期', dataIndex: 'remark', width: 100 },
  { title: '签约日期', dataIndex: 'signDate', width: 120 },
  { title: '业务人员', dataIndex: 'operator_dictText', width: 140 },
  { title: '合同类型', dataIndex: 'contractType_dictText', width: 130 },
  { title: '租金(元/月)', dataIndex: 'wyBuildingCount', width: 140 },
  { title: '开始日期', dataIndex: 'startDate', width: 120 },
  { title: '结束日期', dataIndex: 'expireDate', width: 120 }
]

const visible = ref(false)

const openModal = () => {
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
  if (Array.isArray(modelValue)) {
    selectedRowKeys.value = modelValue
  } else {
    selectedRowKeys.value = [modelValue]
  }
}

const showClear = ref(false)
const showClearBtn = computed(() => modelValue && modelValue.length > 0 && showClear.value)
const onmouseenter = () => {
  if (modelValue) {
    showClear.value = true
  }
}
const onmouseleave = () => {
  showClear.value = false
}

const handleClear = () => {
  emit('update:modelValue', multiple ? [] : '')
  emit('change', multiple ? [] : '')
  displayValue.value = ''
}

const handleConfirm = () => {
  const value = multiple ? selectedRowKeys.value : selectedRowKeys.value[0]
  displayValue.value = selectedRows.value.map((item) => item.contractNumber).join(', ')
  emit('update:modelValue', value)
  emit('change', value)
  handleCancel()
}

const handleCancel = () => {
  visible.value = false
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      pageNo: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

watch(
  () => modelValue,
  async (val) => {
    if (!(val && val.length)) {
      displayValue.value = ''
      return
    }
    if (!selectedRowKeys.value.length) {
      const id = Array.isArray(val) ? val.join(',') : val
      const { result } = await page({ id })
      result.records.forEach((item) => {
        selectedRowKeys.value.push(item.id)
        selectedRows.value.push(item)
      })
      displayValue.value = selectedRows.value.map((item) => item.name).join(', ')
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.contract-select-input {
  width: 100%;
  cursor: pointer;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 14px;
  background-color: #fff;
  transition: border-color 0.2s;
  &:hover {
    border-color: var(--color-primary);
  }
  & > span:empty {
    &::after {
      content: attr(placeholder);
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
</style>
