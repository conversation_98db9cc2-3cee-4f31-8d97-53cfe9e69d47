<template>
  <a-drawer
    v-model:open="visible"
    class="edit-file-archiving-drawer common-drawer"
    :title="`${form.id ? '编辑' : '新建'}价格递增方式`"
    placement="right"
    width="1000px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form :model="form" :rules="rules" ref="formRef" :label-col="{ style: { width: '50px' } }" autocomplete="off">
        <a-form-item label="名称" name="name">
          <a-input v-model:value="form.name" :maxlength="50" show-count></a-input>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="form.status">
            <a-radio value="ENABLE">启用</a-radio>
            <a-radio value="DISABLE">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
      <div class="flex items-center justify-between mt-[40px] mb-[16px]">
        <h4 class="text-[16px] font-bold">价格递增方式</h4>
        <a-button type="primary" size="medium" @click="handleAdd">
          <i class="a-icon-plus"></i>
          添加
        </a-button>
      </div>
      <draggable v-model="form.priceIncreaseWayEntryList" handle=".a-icon-move" item-key="id">
        <template #item="{ element, index }">
          <div class="flex items-center gap-x-[12px] justify-between mb-[16px] last-of-type:mb-[0]">
            <i
              class="a-icon-move cursor-move text-tertiary text-[20px]"
              v-show="form.priceIncreaseWayEntryList.length > 1"
            ></i>
            <a-popconfirm title="是否确认移除？" @confirm="handleRemove(index)">
              <div>
                <i class="a-icon-remove cursor-pointer text-tertiary text-[20px] hover:text-error"></i>
              </div>
            </a-popconfirm>
            <a-input v-model:value="element.yearMonthPeriod" :maxlength="4">
              <template #prefix>第</template>
            </a-input>
            <dict-select
              v-model="element.yearMonthPeriodWay"
              :allow-clear="false"
              code="CT_BASE_ENUM_ContractLeaseFundsPriceIncreaseWay_YearMonthPeriodWay"
            ></dict-select>
            <a-input v-model:value="element.increase" :maxlength="4">
              <template #suffix>%</template>
            </a-input>
          </div>
        </template>
      </draggable>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { edit, add, detail, wayList } from '../apis.js'
import { message } from 'ant-design-vue'
import { intRegexp, moneyRegexp } from '@/utils/validate'
import draggable from 'vuedraggable'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  for (const key in form) {
    form[key] = result[key]
  }
  const { result: list } = await wayList({ id })
  form.priceIncreaseWayEntryList = list
  loading.value = false
}

const form = reactive({
  id: '',
  name: '',
  status: 'ENABLE',
  priceIncreaseWayEntryList: []
})

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

const handleAdd = () => {
  form.priceIncreaseWayEntryList.push({
    id: Math.random().toString(),
    seq: 0, // 序号
    yearMonthPeriod: '', // 第*年/月/期
    yearMonthPeriodWay: 'PeriodIncrease', // 年/月/期起递增，默认选择期
    increase: '', // 递增%
    remark: ''
  })
}

const handleRemove = (index) => {
  form.priceIncreaseWayEntryList.splice(index, 1)
}

// 校验递增方式列表是否合法
const checkListIsValid = () => {
  if (!form.priceIncreaseWayEntryList.length) {
    message.warning('请添加递增方式')
    return false
  }
  const result = form.priceIncreaseWayEntryList.some((item, index) => {
    if (!item.yearMonthPeriod) {
      message.warning(`第${index + 1}行，请输入年/月/期`)
      return true
    }
    if (!intRegexp.test(item.yearMonthPeriod)) {
      message.warning(`第${index + 1}行，请输入正确的年/月/期`)
    }
    if (!item.increase) {
      message.warning(`第${index + 1}行，请输入递增比例`)
      return true
    }
    if (!moneyRegexp.test(item.increase)) {
      message.warning(`第${index + 1}行，请输入正确的递增比例`)
      return true
    }
    return false
  })
  return !result
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  if (!checkListIsValid()) return
  try {
    confirmLoading.value = true
    const params = JSON.parse(JSON.stringify(form))
    params.priceIncreaseWayEntryList = params.priceIncreaseWayEntryList.map((item, index) => ({
      ...item,
      id: item.id.includes('.') ? '' : item.id,
      seq: index + 1
    }))
    form.id ? await edit(params) : await add(params)
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.name = ''
  form.status = 'ENABLE'
  form.priceIncreaseWayEntryList = []
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
