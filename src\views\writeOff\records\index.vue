<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex justify-between !my-[24px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <span class="primary-btn">反核销</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-form autocomplete="off" layout="inline" class="!ml-[40px]">
          <a-form-item label="搜索">
            <s-input
              v-model="search.number"
              placeholder="搜索单据编号"
              class="ml-[10px] !w-[280px]"
              @input="handleInput"
            ></s-input>
          </a-form-item>
          <a-form-item>
            <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
          </a-form-item>
        </a-form>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>

    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    ></a-table>

    <add-edit ref="addEditRef"></add-edit>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产处置单"
      :download-fn="() => exportExcel('资产处置单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import AddEdit from './components/AddEdit.vue'
import { message } from 'ant-design-vue'
import { getPage, exportExcel, importExcel } from './apis'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const search = ref({
  number: undefined
})
const defaultColumns = [
  { title: '单据ID', dataIndex: 'number', width: 150, fixed: true },
  { title: '客户名称', dataIndex: 'manageCompany', width: 150 },
  { title: '合同', dataIndex: 'name', width: 150 },
  { title: '租赁单元', dataIndex: 'ctrlUnit', width: 150 },
  { title: '款项类型', dataIndex: 'name', width: 150 },
  { title: '期数/总期数', dataIndex: 'name', width: 150 },
  { title: '归属账单', dataIndex: 'name', width: 150 },
  { title: '核销金额', dataIndex: 'name', width: 150 },
  { title: '核销时间', dataIndex: 'name', width: 150 },
  { title: '核销人', dataIndex: 'name', width: 150 },
  { title: '应收账单', dataIndex: 'name', width: 150 },
  { title: '收款记录', dataIndex: 'name', width: 150 },
  { title: '应收开始时间', dataIndex: 'name', width: 150 },
  { title: '应收结束时间', dataIndex: 'name', width: 150 }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)
const searchList = reactive([])
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id', true)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}

// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('楼栋数据导出.xls', search)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}
</script>
