<template>
  <a-drawer
    v-model:open="visible"
    class="edit-payment-type-drawer common-drawer"
    :title="form.id ? '编辑房产税计提单' : '新建房产税计提单'"
    placement="right"
    width="1120px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '140px' } }" autocomplete="off">
        <h2 class="text-[16px] font-bold mb-[20px] w-full">基础信息</h2>
        <a-form-item label="计提公司" name="jtCompany">
          <dept-tree-select
            v-model="form.jtCompany"
            type="company"
            placeholder="请选择计提公司"
            @change="resetBill"
          ></dept-tree-select>
        </a-form-item>
        <a-form-item label="业务日期" name="bizDate">
          <a-date-picker
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="form.bizDate"
            placeholder="请选择业务日期"
            class="w-full"
          />
        </a-form-item>
        <a-form-item label="所属年月" name="belongYm">
          <a-date-picker
            picker="month"
            value-format="YYYY-MM"
            format="YYYY-MM"
            v-model:value="form.belongYm"
            placeholder="请选择所属年月"
            class="w-full"
            @change="resetBill"
          />
        </a-form-item>
        <a-form-item label="含未审核合同" name="isIncludeNoAuditContract">
          <a-select v-model:value="form.isIncludeNoAuditContract">
            <a-select-option value="true">是</a-select-option>
            <a-select-option value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注" name="remark" class="!w-full">
          <a-textarea
            v-model:value="form.remark"
            placeholder="请输入备注(选填)"
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 5, maxRows: 5 }"
          ></a-textarea>
        </a-form-item>
        <h2 class="flex justify-between text-[16px] font-bold mb-[20px] w-full mt-[40px]">
          <span>计提明细</span>
          <a-button type="primary" @click="handleCreate">开始计提</a-button>
        </h2>
        <a-table
          :data-source="form.landUseTaxJTBillEntryList"
          :columns="columns"
          row-key="id"
          :scroll="{ x: 1200 }"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'adjustAmount'">
              <a-input-number
                v-model:value="record.adjustAmount"
                addon-after="元"
                class="!w-full"
                @change="() => adjustAmountChange(record)"
              />
            </template>
            <template v-if="column.dataIndex === 'remark'">
              <a-input v-model:value="record.remark" placeholder="请输入备注" />
            </template>
          </template>
        </a-table>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button type="primary" :loading="saveLoading" ghost @click="handleSave">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { add, submit, edit, detail, generateJT, queryBuildingUseTaxJTBill } from '../apis.js'
import { message } from 'ant-design-vue'
import Decimal from 'decimal.js'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.status = result.status
  form.number = result.number
  form.jtCompany = result.jtCompany
  form.bizDate = result.bizDate
  form.belongYm = result.belongYm
  form.isIncludeNoAuditContract = result.isIncludeNoAuditContract
  form.remark = result.remark
  loading.value = false
  loadBillEntryList()
}

const loadBillEntryList = async () => {
  const { result } = await queryBuildingUseTaxJTBill({ id: form.id })
  form.landUseTaxJTBillEntryList = result
}

const columns = [
  { title: '产权名称', dataIndex: 'houseOwner_dictText', width: 160, fixed: 'left' },
  { title: '土地面积(㎡)', dataIndex: 'structureArea', width: 120 },
  { title: '房产类型', dataIndex: 'houseType_dictText', width: 160 },
  { title: '产权计税原值', dataIndex: 'houseTaxOrgValue', width: 150 },
  { title: '视同销售计提税', dataIndex: 'stSaleJtRate', width: 150 },
  { title: '从价月税率', dataIndex: 'leaseMonthRate', width: 150 },
  { title: '从价折扣率', dataIndex: 'priceDiscRate', width: 150 },
  { title: '租赁单元', dataIndex: 'leaseUnit', width: 130 },
  { title: '租赁单元面积', dataIndex: 'leaseUnitStructureArea', width: 130 },
  { title: '单元计费原值', dataIndex: 'leaseUnitTaxOrgValue', width: 130 },
  { title: '从价计税开始年月', dataIndex: 'effectDate', width: 150 },
  { title: '从价计税结束年月', dataIndex: 'taxExpireDate', width: 150 },
  { title: '合同', dataIndex: 'contract', width: 130 },
  { title: '合同状态', dataIndex: 'contractBizStatus', width: 130 },
  { title: '账单明细', dataIndex: 'detailBill', width: 130 },
  { title: '账单状态', dataIndex: 'detailBillBizStatus', width: 130 },
  { title: '增值税率', dataIndex: 'addTaxRate', width: 130 },
  { title: '合同开始时间', dataIndex: 'startDate', width: 130 },
  { title: '合同结束时间', dataIndex: 'endDate', width: 130 },
  { title: '租金收入', dataIndex: 'rentAmount', width: 130 },
  { title: '含税租金收入', dataIndex: 'containTaxRentAmount', width: 130 },
  { title: '客户', dataIndex: 'customer', width: 130 },
  { title: '税金', dataIndex: 'taxAmount', width: 120, fixed: 'right' },
  { title: '调整额', dataIndex: 'adjustAmount', width: 180, fixed: 'right' },
  { title: '调整后税金', dataIndex: 'adjustAfterAmount', width: 120, fixed: 'right' },
  { title: '备注', dataIndex: 'remark', width: 160, fixed: 'right' }
]

const form = reactive({
  id: '',
  status: '',
  number: '',
  jtCompany: '',
  bizDate: '',
  belongYm: '',
  isIncludeNoAuditContract: '',
  remark: '',
  landUseTaxJTBillEntryList: []
})

const rules = {
  jtCompany: [{ required: true, message: '请选择计提公司', trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  belongYm: [{ required: true, message: '请选择所属年月', trigger: 'change' }]
}

const resetBill = () => {
  form.landUseTaxJTBillEntryList = []
}

const adjustAmountChange = (record) => {
  const { adjustAmount, taxAmount } = record
  record.adjustAfterAmount = new Decimal(adjustAmount || 0).plus(new Decimal(taxAmount || 0)).toNumber()
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    await submit(params)
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  try {
    saveLoading.value = true
    form.id ? await edit(form) : await add(form)
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const handleCreate = async () => {
  await formRef.value.validate()
  const { jtCompany, belongYm, isIncludeNoAuditContract } = form
  const { result } = await generateJT({ jtCompany, belongYm, isIncludeNoAuditContract })
  form.landUseTaxJTBillEntryList = result
}

const resetForm = () => {
  form.id = ''
  form.status = ''
  form.number = ''
  form.jtCompany = ''
  form.bizDate = ''
  form.belongYm = ''
  form.isIncludeNoAuditContract = ''
  form.remark = ''
  form.landUseTaxJTBillEntryList = []
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-payment-type-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    .ant-form-item {
      width: 50%;
    }
  }
}
</style>
