<template>
  <a-modal
    v-model:open="visible"
    title="选择源租赁单元"
    width="1200px"
    :mask-closable="false"
    class="common-modal"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, selectedRows, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>

<script setup>
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'

const emit = defineEmits(['change'])

const visible = ref(false)

// 表格列定义
const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 200, ellipsis: true },
  { title: '使用类型', dataIndex: 'useType_dictText', width: 120 },
  { title: '租赁面积(m²)', dataIndex: 'leaseArea', width: 120 },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText', width: 120 },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 120, ellipsis: true },
  { title: '单据状态', dataIndex: 'status_dictText', width: 120 },
  { title: '业务状态', dataIndex: 'bizStatus', width: 120 },
  { title: '配套设施', dataIndex: 'supportFacility', width: 150, ellipsis: true },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '产权用途', dataIndex: 'propertyUse_dictText', width: 120 },
  { title: '所属项目', dataIndex: 'wyProject_dictText', width: 120 },
  { title: '单元编码', dataIndex: 'number', width: 200 }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getLeaseUnitList)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')

/**
 * 打开选择器
 */
const open = (selectedUnits = []) => {
  onTableChange()
  if (selectedUnits && selectedUnits.length > 0) {
    selectedRowKeys.value = selectedUnits.map((unit) => unit.leaseUnitObject.id)
    selectedRows.value = selectedUnits
  }
  visible.value = true
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  emit('change', selectedRows.value)
  handleCancel()
}

/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}

/**
 * 表格变化处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize })
}

defineExpose({ open })
</script>
