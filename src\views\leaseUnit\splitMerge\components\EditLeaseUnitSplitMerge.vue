<template>
  <a-drawer
    v-model:open="visible"
    class="lease-unit-split-merge-edit-drawer common-drawer"
    :title="`${formData.id ? '编辑' : '添加'}租赁单元拆合单`"
    placement="right"
    width="1500px"
    :mask-closable="false"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 5 }">
        <h2 class="text-[16px] font-bold mb-[20px]">基础信息</h2>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="物业管理公司" name="manageCompany" required>
              <dept-tree-select
                v-model="formData.manageCompany"
                placeholder="请选择物业管理公司"
                type="company"
              ></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="业务日期" name="bizDate" required>
              <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="经办人" name="operator" required>
              <user-select
                v-model="formData.operator"
                v-model:display-value="formData.operator_dictText"
                placeholder="请选择经办人"
                title="请选择经办人"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="经办部门" name="operatorDepart" required>
              <dept-tree-select v-model="formData.operatorDepart" placeholder="请选择经办部门"></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="拆合类型" name="splitMergeType" required>
              <dict-select
                v-model="formData.splitMergeType"
                placeholder="请选择拆合类型"
                code="CT_BASE_ENUM_LeaseUnitSplitMergeBill_SplitMergeType"
              ></dict-select>
            </a-form-item>
          </a-col>
        </a-row>
        <h2 class="text-[16px] font-bold mb-[20px]">拆合单元信息</h2>
        <lease-unit-transfer
          v-model:source-units="sourceUnits"
          v-model:target-units="targetUnits"
          :split-merge-type="formData.splitMergeType"
        />
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import {
  submitLeaseUnitSplitMergeBill,
  addLeaseUnitSplitMergeBill,
  editLeaseUnitSplitMergeBill,
  queryLeaseUnitSplitMergeBillEntryOriByMainId,
  queryLeaseUnitSplitMergeBillEntryDestByMainId
} from '../apis'
import LeaseUnitTransfer from './LeaseUnitTransfer.vue'

const visible = ref(false)
const formRef = ref()
const confirmLoading = ref(false)

const rules = {
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  operator: [{ required: true, message: '请选择经办人', trigger: 'change' }],
  operatorDepart: [{ required: true, message: '请选择经办部门', trigger: 'change' }],
  splitMergeType: [{ required: true, message: '请选择拆合类型', trigger: 'change' }]
}

// 表单默认数据
const formDataDefault = reactive({
  id: undefined,
  manageCompany: undefined,
  bizDate: undefined,
  operator: undefined,
  operator_dictText: undefined, // 添加经办人显示文本字段
  operatorDepart: undefined,
  splitMergeType: 'Split',
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined,
  delFlag: undefined
})
const formData = reactive({ ...formDataDefault })

// 源租赁单元列表
const sourceUnits = ref([])
// 目标租赁单元列表
const targetUnits = ref([])

/**
 * 打开编辑抽屉
 */
const open = async (data) => {
  if (data) {
    Object.assign(formData, data)
    const oriRes = await queryLeaseUnitSplitMergeBillEntryOriByMainId({ id: data.id })
    const destRes = await queryLeaseUnitSplitMergeBillEntryDestByMainId({ id: data.id })
    sourceUnits.value = oriRes.result || []
    targetUnits.value = destRes.result || []
  }

  visible.value = true
}

/**
 * 保存表单
 */
const handleSave = async () => {
  if (confirmLoading.value) return
  // await formRef.value.validate()

  confirmLoading.value = true

  if (sourceUnits.value.length === 0) {
    message.error('请至少选择一个源租赁单元')
    confirmLoading.value = false
    return
  }

  if (targetUnits.value.length === 0) {
    message.error('请至少添加一个目标租赁单元')
    confirmLoading.value = false
    return
  }

  const submitData = {
    ...formData,
    leaseUnitSplitMergeBillEntryOriList: sourceUnits.value.map((unit) => ({
      leaseUnit: unit.id,
      leaseUnitObject: { ...unit }
    })),
    leaseUnitSplitMergeBillEntryDestList: targetUnits.value.map((unit) => ({
      leaseUnit: unit.id,
      leaseUnitObject: { ...unit }
    }))
  }

  try {
    if (formData.id) {
      await editLeaseUnitSplitMergeBill(submitData)
      message.success('编辑成功')
    } else {
      await submitLeaseUnitSplitMergeBill(submitData)
      message.success('添加成功')
    }
    emit('refresh')
    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 暂存表单
 */
const handleTemporaryStorage = async () => {
  confirmLoading.value = true

  if (sourceUnits.value.length === 0) {
    message.error('请至少选择一个源租赁单元')
    confirmLoading.value = false
    return
  }

  if (targetUnits.value.length === 0) {
    message.error('请至少添加一个目标租赁单元')
    confirmLoading.value = false
    return
  }

  const submitData = {
    ...formData,
    leaseUnitSplitMergeBillEntryOriList: sourceUnits.value.map((unit) => ({
      leaseUnit: unit.id,
      leaseUnitObject: { ...unit }
    })),
    leaseUnitSplitMergeBillEntryDestList: targetUnits.value.map((unit) => ({
      leaseUnit: unit.id,
      leaseUnitObject: { ...unit }
    }))
  }
  try {
    if (formData.id) {
      await editLeaseUnitSplitMergeBill(submitData)
      message.success('编辑成功')
    } else {
      await addLeaseUnitSplitMergeBill(submitData)
      message.success('暂存成功')
    }
    emit('refresh')
    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 取消编辑
 */
const handleCancel = () => {
  visible.value = false
  Object.assign(formData, formDataDefault)
  sourceUnits.value = []
  targetUnits.value = []
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const emit = defineEmits(['refresh'])

defineExpose({ open })
</script>

<style scoped>
.lease-unit-split-merge-edit-drawer :deep(.lease-unit-transfer) {
  margin-bottom: 24px;
}

.lease-unit-split-merge-edit-drawer :deep(.transfer-container) {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.lease-unit-split-merge-edit-drawer :deep(.transfer-list-item:hover) {
  background-color: #f5f5f5;
}
</style>
