<template>
  <div class="lease-unit-select">
    <a-input :value="displayValue" :placeholder="placeholder" readonly @click="handleClick" class="selector-input">
      <template #suffix>
        <i class="a-icon-arrow-down"></i>
        <i v-if="modelValue" class="a-icon-close-solid text-tertiary cursor-pointer" @click.stop="handleClear"></i>
      </template>
    </a-input>

    <a-modal
      v-model:open="visible"
      title="选择租赁单元"
      width="1200px"
      :mask-closable="false"
      class="common-modal"
      @ok="handleConfirm"
      @cancel="handleCancel"
    >
      <!-- 搜索区域 -->
      <div class="flex my-[16px]">
        <s-input
          v-model="searchParams.name"
          placeholder="搜索租赁单元名称"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input>
      </div>

      <a-table
        :data-source="list"
        :columns="columns"
        :loading="tableLoading"
        :pagination="pagination"
        row-key="id"
        :row-selection="{ selectedRowKeys, selectedRows, onChange: onSelectChange, type: 'radio' }"
        :scroll="{ x: 1500, y: tableHeight }"
        @change="onTableChange"
      ></a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'

const props = defineProps({
  modelValue: { type: [String, Number], default: undefined },
  displayValue: { type: String },
  placeholder: { type: String, default: '请选择租赁单元' }
})

const emit = defineEmits(['update:modelValue', 'update:displayValue', 'change'])

const visible = ref(false)

// 搜索参数
const searchParams = reactive({
  name: undefined
})

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getLeaseUnitList)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

// 表格列定义
const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 200, ellipsis: true },
  { title: '使用类型', dataIndex: 'useType_dictText', width: 120 },
  { title: '租赁面积(m²)', dataIndex: 'leaseArea', width: 120 },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText', width: 120 },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 120, ellipsis: true },
  { title: '单据状态', dataIndex: 'status_dictText', width: 120 },
  { title: '业务状态', dataIndex: 'bizStatus', width: 120 },
  { title: '配套设施', dataIndex: 'supportFacility', width: 150, ellipsis: true },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '产权用途', dataIndex: 'propertyUse_dictText', width: 120 },
  { title: '所属项目', dataIndex: 'wyProject_dictText', width: 120 },
  { title: '单元编码', dataIndex: 'number', width: 200 }
]

// 监听modelValue变化
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal) {
      // 在列表中查找对应的记录
      let selectedUnit = list.value.find((item) => item.id === newVal)

      if (!selectedUnit) {
        // 如果当前列表中没有，则重新加载数据
        await onTableFetch({ pageNo: 1, pageSize: pagination.value.pageSize })
        selectedUnit = list.value.find((item) => item.id === newVal)
      }

      if (selectedUnit) {
        selectedRowKeys.value = [selectedUnit.id]
        selectedRows.value = [selectedUnit]
        emit('update:displayValue', selectedUnit.name)
      }
    } else {
      emit('update:displayValue', undefined)
      clearSelection()
    }
  },
  { immediate: true }
)

// 点击输入框
const handleClick = () => {
  visible.value = true
  if (!list.value.length) {
    onTableChange()
  }
}

// 清除选择
const handleClear = (e) => {
  e.stopPropagation()
  emit('update:modelValue', undefined)
  emit('update:displayValue', undefined)
  emit('change', undefined)
  clearSelection()
}

// 表格变化处理
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      current: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

// 确认选择
const handleConfirm = () => {
  if (selectedRows.value?.[0]) {
    const selectedUnit = selectedRows.value[0]
    emit('update:modelValue', selectedUnit.id)
    emit('update:displayValue', selectedUnit.name)
    emit('change', selectedUnit)
    visible.value = false
  } else {
    message.warning('请选择记录')
  }
}

// 取消选择
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.lease-unit-select {
  display: inline-block;
  width: 100%;
}

.selector-input {
  cursor: pointer;
  background-color: #fff;
}

.selector-input :deep(.ant-input) {
  cursor: pointer;
}

.selector-input :deep(.ant-input-suffix) {
  display: flex;
  align-items: center;
}

.selector-input :deep(.a-icon-arrow-down) {
  color: rgba(0, 0, 0, 0.25);
  font-size: 12px;
  margin-right: 6px;
  transition: transform 0.3s;
}

.selector-input:hover :deep(.a-icon-arrow-down) {
  color: #000;
}
</style>
