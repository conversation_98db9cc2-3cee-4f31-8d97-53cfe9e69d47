import { useDictStore } from '@/store/modules/dict'
import StatusTag from '@/components/StatusTag.vue'

const getDictItem = (value, code) => {
  if (typeof value === 'number') {
    value = String(value)
  }
  if (!value || !code) return ''
  const store = useDictStore()
  if (!store.dict) return ''
  if (!Object.keys(store.dict).length) return ''
  const list = store.dict[code]
  if (!(list && list.length)) return ''
  return list.find((i) => i.value === String(value))
}

export const renderDict = (value, code) => {
  const data = getDictItem(value, code)
  return data ? data.label : ''
}

/**
 * 在a-table中以tag样式渲染字典值
 * @param {String} value 字典值
 * @param {String} code 字典编码
 * @param {String} type tag=经典标签样式 dot=前面有一个圆点的样式
 * @param {String} color 自定义颜色值
 */
export const renderDictTag = (value, code, type, color) => {
  const data = getDictItem(value, code)
  if (!data) return ''
  return h(
    StatusTag,
    {
      dictValue: value,
      dictCode: code,
      color: color || (data && data.color),
      type
    },
    () => data.label
  )
}

export const renderBoolean = (value) => {
  return value ? '是' : '否'
}
