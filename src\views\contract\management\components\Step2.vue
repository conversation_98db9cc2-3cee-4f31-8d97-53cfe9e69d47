<template>
  <div>
    <div class="flex items-center justify-between">
      <strong class="text-[16px]">合同款项</strong>
      <a-dropdown>
        <a-button type="primary" size="medium">
          添加款项
          <i class="a-icon-arrow-down ml-[8px]"></i>
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item v-for="item in paymentTypeList" :key="item.id" @click="handleSelectPaymentType(item)">
              {{ item.name }}
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <div class="h-[36px] flex items-center bg-[#f5f7fa] my-[20px] text-secondary px-[16px] rounded-[8px]">
      <i class="a-icon-tips mr-[8px]"></i>
      说明: 水电费将根据租赁单元对应的水电均摊方式自动出账，无需在此定义。
    </div>
    <ul>
      <li v-for="(item, index) in form.contractLeaseFundsList" :key="item.id" class="mb-[20px] last-of-type:mb-[0]">
        <div class="flex items-center justify-between mb-[10px]">
          <strong>{{ item.paymentTypeName }}</strong>
          <a-popconfirm title="是否确认移除？" @confirm="handleRemove(index)">
            <span>
              <i class="a-icon-remove text-[18px] cursor-pointer text-error"></i>
            </span>
          </a-popconfirm>
        </div>
        <a-table :data-source="[item]" :columns="columns" :pagination="false" bordered :scroll="{ x: 1200 }">
          <template #bodyCell="{ column }">
            <template v-if="column.dataIndex === 'period'">
              <a-input v-model:value="item.period">
                <template #suffix>月</template>
              </a-input>
            </template>
            <template v-if="column.dataIndex === 'amountPerMonth'">
              <a-input v-model:value="item.amountPerMonth">
                <template #suffix>元/月</template>
              </a-input>
            </template>
            <template v-if="column.dataIndex === 'startDate'">
              <a-range-picker v-model:value="item.dateRange" value-format="YYYY-MM-DD" />
            </template>
            <template v-if="column.dataIndex === 'createDetailBill'">
              <dict-select
                v-model="item.createDetailBill"
                code="CT_BASE_ENUM_Contract_CreateDetailBill"
                style="width: 180px"
              ></dict-select>
            </template>
            <template v-if="column.dataIndex === 'advanceDays'">
              <a-input v-model:value="item.advanceDays">
                <template #prefix>比开始日期提前</template>
                <template #suffix>天</template>
              </a-input>
            </template>
            <template v-if="column.dataIndex === 'remark'">
              <a-input v-model:value="item.remark"></a-input>
            </template>
          </template>
        </a-table>
        <div class="flex items-center justify-between mt-[20px] mb-[12px]">
          <strong>递增规则</strong>
          <a-dropdown>
            <a-button type="primary" size="medium">
              添加规则
              <i class="a-icon-arrow-down ml-[8px]"></i>
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item v-for="rule in ruleList" :key="rule.id" @click="handleSelectRule(item, rule)">
                  {{ rule.name }}
                </a-menu-item>
                <a-menu-item @click="handleAddCustomRule(item)">自定义规则</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        <ul>
          <li
            class="flex items-center gap-x-[12px] justify-between mb-[16px] last-of-type:mb-[0]"
            v-for="(rule, ruleIndex) in item.contractLeaseFundsPriceIncreaseWayList"
            :key="rule.id"
          >
            <a-popconfirm title="是否确认移除？" @confirm="handleRemoveRule(item, ruleIndex)">
              <div>
                <i class="a-icon-remove cursor-pointer text-tertiary text-[20px] hover:text-error"></i>
              </div>
            </a-popconfirm>
            <a-input v-model:value="rule.yearMonthPeriod" :maxlength="4">
              <template #prefix>第</template>
            </a-input>
            <dict-select
              v-model="rule.yearMonthPeriodWay"
              :allow-clear="false"
              code="CT_BASE_ENUM_ContractLeaseFundsPriceIncreaseWay_YearMonthPeriodWay"
            ></dict-select>
            <a-input v-model:value="rule.increase" :maxlength="4">
              <template #suffix>%</template>
            </a-input>
          </li>
        </ul>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { getContractFundTypeList } from '../apis'
import { message } from 'ant-design-vue'
import { intRegexp, moneyRegexp, positionIntRegexp } from '@/utils/validate'
import { page, wayList } from '@/views/contract/priceIncrease/apis'

const { form } = defineProps({
  form: { required: true, type: Object }
})

const columns = [
  { title: '缴交周期', dataIndex: 'period' },
  { title: '金额', dataIndex: 'amountPerMonth' },
  { title: '起止日期', dataIndex: 'startDate', width: 280 },
  { title: '账单生成规则', dataIndex: 'createDetailBill' },
  { title: '应收日', dataIndex: 'advanceDays' },
  { title: '备注', dataIndex: 'remark' }
]

const paymentTypeList = ref([])
const loadPaymentTypeList = async () => {
  const { result } = await getContractFundTypeList()
  paymentTypeList.value = result.records
}

const handleSelectPaymentType = (item) => {
  if (form.contractLeaseFundsList.some((i) => i.paymentType === item.id)) {
    message.warning(`您已添加了“${item.name}”，请勿重复添加`)
    return
  }
  form.contractLeaseFundsList.push({
    id: Math.random().toString(),
    paymentType: item.id,
    paymentTypeName: item.name,
    period: '', // 缴交周期
    amountPerMonth: '', // 金额
    amountPerPeriod: '', // 每期金额，自动生成（缴费期数*月租金）
    startDate: '',
    expireDate: '',
    dateRange: [], // startDate && expireDate
    createDetailBill: '', // 账单生成规则
    advanceDays: '', // 应收提前天数
    remark: '',
    contractLeaseFundsPriceIncreaseWayList: [] // 价格递增方式列表
  })
}

const handleRemove = (index) => {
  form.contractLeaseFundsList.splice(index, 1)
}

const ruleList = ref([]) // 递增规则列表
const loadRuleList = async () => {
  const { result } = await page()
  ruleList.value = result.records
}

const handleSelectRule = async (item, rule) => {
  const { result } = await wayList({ id: rule.id })
  item.contractLeaseFundsPriceIncreaseWayList = result.map((i) => ({
    id: Math.random().toString(),
    isSavePriceIncreaseWay: false, // 是否同步保存到价格递增方式的增删改查那边
    yearMonthPeriod: i.yearMonthPeriod,
    yearMonthPeriodWay: i.yearMonthPeriodWay,
    increase: i.increase
  }))
}

const handleAddCustomRule = (item) => {
  item.contractLeaseFundsPriceIncreaseWayList.push({
    id: Math.random().toString(),
    isSavePriceIncreaseWay: false, // 是否同步保存到价格递增方式的增删改查那边
    yearMonthPeriod: '', // 第*年/月/期
    yearMonthPeriodWay: 'PeriodIncrease', // 年/月/期起递增，默认选择期
    increase: ''
  })
}

const handleRemoveRule = (item, index) => {
  item.contractLeaseFundsPriceIncreaseWayList.splice(index, 1)
}

const validate = () => {
  if (!form.contractLeaseFundsList) {
    message.warning('请添加合同款项')
    return false
  }
  const result = form.contractLeaseFundsList.some((item) => {
    if (!item.period) {
      message.warning(`“${item.paymentTypeName}”请填写缴交周期`)
      return true
    }
    if (!intRegexp.test(item.period)) {
      message.warning(`“${item.paymentTypeName}”缴交周期填写不正确`)
      return true
    }
    if (!item.amountPerMonth) {
      message.warning(`“${item.paymentTypeName}”请填写金额`)
      return true
    }
    if (!moneyRegexp.test(item.amountPerMonth)) {
      message.warning(`“${item.paymentTypeName}”金额填写不正确`)
      return true
    }
    if (!(item.dateRange && item.dateRange.length)) {
      message.warning(`“${item.paymentTypeName}”请选择起止日期`)
      item.startDate = ''
      item.expireDate = ''
      return true
    }
    if (!item.createDetailBill) {
      message.warning(`“${item.paymentTypeName}”请选择账单生成规则`)
      return true
    }
    if (!item.advanceDays) {
      message.warning(`“${item.paymentTypeName}”请填写应收日`)
      return true
    }
    if (!positionIntRegexp.test(item.advanceDays)) {
      message.warning(`“${item.paymentTypeName}”应收日填写不正确`)
      return true
    }
    if (item.contractLeaseFundsPriceIncreaseWayList && item.contractLeaseFundsPriceIncreaseWayList.length) {
      const isValid = item.contractLeaseFundsPriceIncreaseWayList.some((rule, ruleIndex) => {
        if (!rule.yearMonthPeriod) {
          message.warning(`“${item.paymentTypeName}”递增规则的第${ruleIndex + 1}条，请填写第几年/月/期`)
          return true
        }
        if (!intRegexp.test(rule.yearMonthPeriod)) {
          message.warning(`“${item.paymentTypeName}”递增规则的第${ruleIndex + 1}条，第几年/月/期填写不正确`)
          return true
        }
        if (!rule.increase) {
          message.warning(`“${item.paymentTypeName}”递增规则的第${ruleIndex + 1}条，请填写递增比例`)
          return true
        }
        if (!moneyRegexp.test(rule.increase)) {
          message.warning(`“${item.paymentTypeName}”递增规则的第${ruleIndex + 1}条，递增比例填写不正确`)
          return true
        }
        return false
      })
      return isValid
    }
    return false
  })
  return !result
}

onMounted(() => {
  loadPaymentTypeList()
  loadRuleList()
})

defineExpose({ validate })
</script>

<style lang="less" scoped>
:deep(.ant-table-wrapper) {
  .ant-table-cell {
    border-top: none;
  }
  .ant-table-cell:first-child {
    border-left: none;
  }
  .ant-table-cell:last-child {
    border-right: none;
  }
}
</style>
