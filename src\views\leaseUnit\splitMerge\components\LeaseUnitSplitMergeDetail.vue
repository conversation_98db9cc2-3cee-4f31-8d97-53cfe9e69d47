<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1500px"
    :mask-closable="false"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitchDetail(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchDetail(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn">撤回</span>
          <span class="primary-btn">催办</span>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">拆合单详情</h2>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_BaseStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <!-- 基础信息 -->
      <div class="mb-[40px]">
        <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">
            <span class="text-tertiary">物业管理公司：</span>
            {{ detailData.manageCompany_dictText || '-' }}
          </span>
          <span class="w-[50%]">
            <span class="text-tertiary">业务日期：</span>
            {{ detailData.bizDate || '-' }}
          </span>
          <span class="w-[50%]">
            <span class="text-tertiary">经办人：</span>
            {{ detailData.operator_dictText || '-' }}
          </span>
          <span class="w-[50%]">
            <span class="text-tertiary">经办部门：</span>
            {{ detailData.operatorDepart_dictText || '-' }}
          </span>
          <span class="w-[50%]">
            <span class="text-tertiary">拆合类型：</span>
            {{ detailData.splitMergeType_dictText || '-' }}
          </span>
          <span class="w-[100%]">
            <span class="text-tertiary">备注：</span>
            {{ detailData.remark || '-' }}
          </span>
        </div>
      </div>

      <!-- 拆合单元信息 -->
      <div class="mb-[40px]">
        <h2 class="text-[16px] font-bold mb-[12px]">拆合单元信息</h2>
        <div class="flex items-stretch border border-gray-200 rounded-lg overflow-hidden">
          <div class="flex-1 flex flex-col min-h-[300px] bg-white source-list">
            <div class="flex justify-between items-center py-3 px-4 border-b border-gray-200">
              <div class="font-medium text-base">源单元</div>
            </div>
            <div class="flex-1 overflow-y-auto">
              <div v-if="!oriEntryList?.length" class="flex items-center justify-center h-full min-h-[200px]">
                <a-empty description="暂无数据" />
              </div>
              <div v-else class="p-2">
                <div
                  v-for="item in oriEntryList"
                  :key="item.id"
                  class="flex justify-between items-start p-3 mb-2 border border-gray-200 rounded"
                >
                  <div class="flex-1 overflow-hidden">
                    <div class="font-medium mb-2 whitespace-nowrap overflow-hidden text-ellipsis">
                      {{ item.leaseUnitObject.name }} ({{ item.leaseUnitObject.number }})
                    </div>
                    <div class="text-xs text-gray-600">
                      <div class="flex mt-1">
                        <span class="flex-1">
                          地址：{{ item.leaseUnitObject.province }}{{ item.leaseUnitObject.city
                          }}{{ item.leaseUnitObject.district }}{{ item.leaseUnitObject.detailAddress }}
                        </span>
                        <span class="flex-1">租赁面积：{{ item.leaseUnitObject.leaseArea }}m²</span>
                      </div>
                      <div class="flex mt-1">
                        <span class="flex-1">产权：{{ item.leaseUnitObject.propertyUse_dictText }}</span>
                        <span class="flex-1">租赁用途：{{ item.leaseUnitObject.leaseUse_dictText }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col items-end">
                    <a-button type="link" @click="viewUnitDetail(item)">
                      <i class="a-icon-eye"></i>
                    </a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex items-center justify-center w-[50px] bg-gray-100">
            <div class="w-6 h-6 flex items-center justify-center text-blue-500 text-base">
              <i class="a-icon-arrow-right"></i>
            </div>
          </div>

          <div class="flex-1 flex flex-col min-h-[300px] bg-white target-list">
            <div class="flex justify-between items-center py-3 px-4 border-b border-gray-200">
              <div class="font-medium text-base">新单元</div>
            </div>
            <div class="flex-1 overflow-y-auto">
              <div v-if="!destEntryList?.length" class="flex items-center justify-center h-full min-h-[200px]">
                <a-empty description="暂无数据" />
              </div>
              <div v-else class="p-2">
                <div
                  v-for="item in destEntryList"
                  :key="item.id"
                  class="flex justify-between items-start p-3 mb-2 border border-gray-200 rounded"
                >
                  <div class="flex-1 overflow-hidden">
                    <div class="font-medium mb-2 whitespace-nowrap overflow-hidden text-ellipsis">
                      {{ item.leaseUnitObject.name }} ({{ item.leaseUnitObject.number }})
                    </div>
                    <div class="text-xs text-gray-600">
                      <div class="flex mt-1">
                        <span class="flex-1">
                          地址：{{ item.leaseUnitObject.province }}{{ item.leaseUnitObject.city
                          }}{{ item.leaseUnitObject.district }}{{ item.leaseUnitObject.detailAddress }}
                        </span>
                        <span class="flex-1">租赁面积：{{ item.leaseUnitObject.leaseArea }}m²</span>
                      </div>
                      <div class="flex mt-1">
                        <span class="flex-1">产权：{{ item.leaseUnitObject.propertyUse_dictText }}</span>
                        <span class="flex-1">租赁用途：{{ item.leaseUnitObject.leaseUse_dictText }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col items-end">
                    <a-button type="link" @click="viewUnitDetail(item)">
                      <i class="a-icon-eye"></i>
                    </a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import {
  getLeaseUnitSplitMergeBillById,
  queryLeaseUnitSplitMergeBillEntryOriByMainId,
  queryLeaseUnitSplitMergeBillEntryDestByMainId
} from '../apis'

const visible = ref(false)
const loading = ref(false)
const detailData = ref({})
const oriEntryList = ref([])
const destEntryList = ref([])
const currentId = ref('')

const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const currentIndex = computed(() => {
  if (!detailData.value.id) return 0
  return dataList.findIndex((i) => i.id === detailData.value.id)
})

/**
 * 切换详情
 */
const handleSwitchDetail = (index) => {
  loadDetail(dataList[index].id)
}

/**
 * 打开详情抽屉
 */
const open = async (record) => {
  if (!record || !record.id) {
    message.error('缺少必要参数')
    return
  }

  currentId.value = record.id
  visible.value = true
  await loadDetail(record.id)
}

/**
 * 获取详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  const res = await getLeaseUnitSplitMergeBillById({ id })
  const oriRes = await queryLeaseUnitSplitMergeBillEntryOriByMainId({ id })
  const destRes = await queryLeaseUnitSplitMergeBillEntryDestByMainId({ id })
  if (res.success) {
    detailData.value = res.result || {}
    oriEntryList.value = oriRes.result || []
    destEntryList.value = destRes.result || []
  } else {
    message.error(res.message || '获取详情失败')
  }
  loading.value = false
}

/**
 * 查看租赁单元详情
 */
const emit = defineEmits(['view-unit'])
const viewUnitDetail = (unit) => {
  emit('view-unit', unit)
}

/**
 * 关闭抽屉
 */
const handleClose = () => {
  visible.value = false
  detailData.value = {}
}

defineExpose({
  open
})
</script>
