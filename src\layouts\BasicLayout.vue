<template>
  <header class="h-[64px] bg-white flex items-center justify-between border-b border-solid border-[#d7dae0] px-[24px]">
    <div>logo</div>
    <a-dropdown>
      <div class="flex items-center cursor-pointer" @click.prevent>
        <img src="@/assets/imgs/avatar.png" class="w-[32px] h-[32px] rounded-[50%]" />
        <span class="ml-[12px] mr-[4px]">你好，{{ userInfo.realname }}</span>
        <i class="a-icon-arrow-down text-[14px] text-[#cecece]"></i>
      </div>
      <template #overlay>
        <a-menu>
          <a-menu-item @click="enterPersonalCenter">基础信息</a-menu-item>
          <a-menu-item @click="handleUpdatePassword">密码修改</a-menu-item>
          <a-menu-item @click="handleLogout">退出登录</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </header>
  <main class="flex">
    <aside class="basic-layout-aside h-[calc(100vh-64px)] pt-[32px] border-r border-solid border-[#d7dae0]">
      <div class="flex items-center justify-center h-[28px] mb-[24px] px-[16px]">
        <h2 class="text-[18px] font-bold flex-1 line-clamp-1 max-w-[222px]" v-show="!state.collapsed">资产管理</h2>
        <i
          class="a-icon-pack-up text-[20px] cursor-pointer hover:text-primary transition-colors"
          @click="state.collapsed = !state.collapsed"
        ></i>
      </div>
      <a-menu
        v-model:open-keys="state.openKeys"
        v-model:selected-keys="state.selectedKeys"
        mode="inline"
        :inline-collapsed="state.collapsed"
        :items="menus"
        @click="handleMenuClick"
        class="aside-menu"
      ></a-menu>
    </aside>
    <section class="flex-1 h-[calc(100vh-64px)] overflow-hidden bg-[#f7f8fa] p-[16px]">
      <router-view
        id="basic-router-view"
        class="bg-white rounded-[8px] p-[16px] h-full overflow-auto scrollbar"
      ></router-view>
    </section>
  </main>
  <a-modal
    v-model:open="visible"
    title="修改密码"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form :model="form" ref="formRef" :rules="rules" autocomplete="off" :label-col="{ style: { width: '84px' } }">
      <a-form-item label="旧密码" name="oldpassword">
        <a-input-password v-model:value="form.oldpassword" :maxlength="20"></a-input-password>
      </a-form-item>
      <a-form-item label="新密码" name="password">
        <a-input-password v-model:value="form.password" :maxlength="20"></a-input-password>
      </a-form-item>
      <a-form-item label="确认密码" name="confirmpassword">
        <a-input-password v-model:value="form.confirmpassword" :maxlength="20"></a-input-password>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { useUserStore } from '@/store/modules/user'
import { Modal, message } from 'ant-design-vue'
import { updatePassword } from '@/apis/user'
import { validatePassword } from '@/utils/validate'

const store = useUserStore()
const router = useRouter()
const route = useRoute()
const userInfo = computed(() => store.userInfo)

const menus = computed(() => store.permission.menu.map(getMenuItem))

const getMenuItem = (item) => {
  return {
    key: item.id,
    icon: () => h('i', { class: item.meta.icon }),
    label: item.meta.title,
    title: item.meta.title,
    path: item.path,
    children: item.children ? item.children.map(getMenuItem) : undefined
  }
}

const state = reactive({
  collapsed: false,
  selectedKeys: [],
  openKeys: []
})

const handleMenuClick = ({ item }) => {
  router.push(item.path)
}

const handleLogout = () => {
  Modal.confirm({
    title: '系统提示',
    content: '是否确认退出登录？',
    centered: true,
    onOk: () => {
      store.logout()
    }
  })
}

// 进入个人中心
const enterPersonalCenter = () => {
  router.push({ path: '/personalCenter/index' })
}

const visible = ref(false)
const handleUpdatePassword = () => {
  visible.value = true
}

const form = reactive({
  confirmpassword: '',
  oldpassword: '',
  password: ''
})
const rules = computed(() => ({
  oldpassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  password: [{ required: true, validator: validatePassword('新密码'), trigger: 'blur' }],
  confirmpassword: [{ required: true, validator: validatePassword('确认密码', form.password), trigger: 'blur' }]
}))

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    await updatePassword({ ...form, username: userInfo.value.username })
    confirmLoading.value = false
    handleCancel()
    message.success('密码修改成功，请重新登录')
    store.logout()
  } catch {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  formRef.value.resetFields()
  visible.value = false
}

/**
 * 根据当前路由地址，获取其在菜单树中的访问路径
 * @param {Array} list 菜单树
 * @param {String} path 当前路由
 * @return {String} 访问路径，即祖级id/父级id/子级id
 */
const getMenuIdByPath = (list, path) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].path === path) return list[i].key
    if (list[i].children) {
      const childPath = getMenuIdByPath(list[i].children, path)
      if (childPath) return `${list[i].key}/${childPath}`
    }
  }
}

watchEffect(() => {
  const path = getMenuIdByPath(menus.value, route.path)
  if (!path) {
    state.selectedKeys = []
    state.openKeys = []
    return
  }
  const list = path.split('/')
  state.selectedKeys = [list[list.length - 1]]
  state.openKeys = list.slice(0, list.length - 1)
})
</script>

<style lang="less" scoped>
.basic-layout-aside {
  :deep(.aside-menu) {
    width: 270px;
    height: calc(100% - 52px);
    border-inline-end: none;
    overflow-y: auto;
    .no-scrollbar();
  }
  :deep(.ant-menu-title-content) {
    font-size: 16px;
  }
  :deep(.ant-menu-item-icon) {
    font-size: 18px;
  }
  :deep(.ant-menu-sub) {
    border-left: 1px solid #d7dae0;
    margin-left: 32px;
    background: #fff !important;
    .ant-menu-item {
      padding: 0 !important;
      margin-inline: 0;
      margin-block: 0;
      margin-left: 12px;
      width: calc(100% - 28px);
      line-height: 48px;
      height: 48px;
      &:hover {
        background-color: transparent;
        color: var(--color-primary);
      }
      &-selected {
        background-color: var(--color-primary) !important;
        color: #fff !important;
      }
    }
    .ant-menu-title-content {
      margin-left: 20px;
    }
    i {
      display: none;
    }
  }
}
</style>
