<template>
  <a-select
    ref="selectRef"
    v-model:value="selectedTreeNode"
    placeholder="请选择类别"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :dropdown-match-select-width="false"
    class="!ml-[40px] !w-[280px]"
    allow-clear
    @clear="handleClear"
  >
    <template #dropdownRender>
      <div @mousedown.prevent>
        <a-tree v-model:selected-keys="selectedKeys" :tree-data="treeData" default-expand-all @select="onTreeSelect">
          <template #title="{ name, id }">
            <div class="flex items-center group h-[24px]">
              <span>{{ name }}</span>
              <div class="hidden group-hover:flex">
                <a-tooltip title="添加下级">
                  <span class="mx-1 cursor-pointer text-primary">
                    <i class="a-icon-plus" @click.stop="handleAddTreeNode(id)"></i>
                  </span>
                </a-tooltip>
                <a-tooltip title="编辑">
                  <span class="mx-1 cursor-pointer text-primary">
                    <i class="a-icon-edit" @click.stop="handleEditTreeNode(id)"></i>
                  </span>
                </a-tooltip>
                <a-tooltip title="删除">
                  <span class="mx-1 cursor-pointer text-[#ff4d4f]">
                    <i class="a-icon-remove" @click.stop="handleDeleteTreeNode(id)"></i>
                  </span>
                </a-tooltip>
              </div>
            </div>
          </template>
        </a-tree>
        <div class="flex justify-center border-t border-solid border-[#f0f0f0] mt-2 pt-2">
          <span class="primary-btn" @click="handleAddRootTreeNode">添加根节点</span>
        </div>
      </div>
    </template>
    <a-select-option v-if="selectedName" :value="selectedTreeNode">
      {{ selectedName }}
    </a-select-option>
  </a-select>

  <!-- 编辑弹窗 -->
  <edit-lease-unit-tree-modal ref="editLeaseUnitTreeModalRef" @success="handleEditSuccess" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { getLeaseUnitTree, deleteLeaseUnitTree } from '../apis/leaseUnitTree'
import EditLeaseUnitTreeModal from './EditLeaseUnitTreeModal.vue'

// 定义 emit
const emit = defineEmits(['treeNodeChange'])

// ref
const selectRef = ref()
const editLeaseUnitTreeModalRef = ref(null)

// 响应式数据
const treeData = ref([])
const selectedTreeNode = ref()
const selectedName = ref('')
const selectedKeys = ref([])

// 业务逻辑函数
const fetchTreeData = async () => {
  const { result } = await getLeaseUnitTree()
  treeData.value = result || []
}

const onTreeSelect = (selectedKeys, { node }) => {
  if (selectedKeys.length) {
    selectedTreeNode.value = node.id
    selectedName.value = node.name
    // 选中节点后，让下拉框收起
    if (selectRef.value) {
      selectRef.value.blur()
    }
    emit('treeNodeChange', node.id)
  }
}

const handleClear = () => {
  selectedTreeNode.value = undefined
  selectedName.value = ''
  selectedKeys.value = []
  emit('treeNodeChange', undefined)
}

// 弹窗相关函数
const handleEditSuccess = () => {
  fetchTreeData()
}

const showEditModal = (editData = null, parentId = '') => {
  editLeaseUnitTreeModalRef.value?.open(editData, parentId)
}

const handleAddRootTreeNode = () => {
  showEditModal()
}

const handleAddTreeNode = (parentId) => {
  showEditModal(null, parentId)
}

const handleEditTreeNode = (id) => {
  const editNode = findNodeById(treeData.value, id)
  if (editNode) {
    showEditModal(editNode)
  }
}

const findNodeById = (nodes, id) => {
  for (const node of nodes) {
    if (node.id === id) {
      return node
    }
    if (node.children) {
      const found = findNodeById(node.children, id)
      if (found) return found
    }
  }
  return null
}

const handleDeleteTreeNode = (id) => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除此节点吗？删除后无法恢复！',
    async onOk() {
      await deleteLeaseUnitTree({ id })
      message.success('删除成功')
      fetchTreeData()
    }
  })
}

// 监听与生命周期
watch(selectedTreeNode, (newVal) => {
  if (!newVal) {
    selectedName.value = ''
    selectedKeys.value = []
  }
})

onMounted(() => {
  fetchTreeData()
})
</script>

<style scoped lang="less">
.group:hover {
  .hidden {
    display: flex;
  }
}
</style>
