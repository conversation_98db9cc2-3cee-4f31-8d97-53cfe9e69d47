<template>
  <div class="house-owner-selector">
    <a-input
      :value="displayValue"
      :placeholder="placeholder"
      readonly
      :style="style"
      @click="handleClick"
      class="selector-input"
    >
      <template #suffix>
        <i class="a-icon-arrow-down"></i>
        <i v-if="modelValue" class="a-icon-close-solid text-tertiary cursor-pointer" @click.stop="handleClear"></i>
      </template>
    </a-input>

    <a-modal
      v-model:open="visible"
      title="选择资产"
      width="1000px"
      wrap-class-name="common-modal"
      :mask-closable="false"
      @ok="handleConfirm"
      @cancel="handleCancel"
    >
      <!-- 搜索区域 -->
      <div class="flex my-[16px]">
        <s-input
          v-model="searchParams.name"
          placeholder="搜索名称"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input>
      </div>

      <!-- 表格区域 -->
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="tableLoading"
        :pagination="pagination"
        row-key="id"
        :row-selection="{ selectedRowKeys, selectedRows, onChange: onSelectChange, type: 'radio' }"
        @change="onTableChange"
        :scroll="{ y: tableHeight, x: 1500 }"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getPage } from '@/views/assets/manage/apis'

const props = defineProps({
  modelValue: { type: [String, Number], default: undefined },
  displayValue: { type: String, default: '' },
  placeholder: { type: String, default: '请选择' },
  style: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['update:modelValue', 'update:displayValue', 'change'])

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const visible = ref(false)

// 搜索参数
const searchParams = reactive({
  name: undefined
})

// 表格列定义
const columns = [
  { title: '资产名称', dataIndex: 'name', fixed: 'left', width: 200 },
  { title: '资产编号', dataIndex: 'number', width: 120 },
  { title: '单据状态', dataIndex: 'status_dictText', width: 100 },
  { title: '资产类型', dataIndex: 'assetsType_dictText', width: 120 },
  { title: '所属项目', dataIndex: 'wyProject_dictText', width: 150 },
  { title: '所属楼栋', dataIndex: 'wyBuilding_dictText', width: 120 },
  { title: '所属楼层', dataIndex: 'wyFloor_dictText', width: 100 },
  { title: '地址', dataIndex: 'detailAddress', ellipsis: true, width: 200 },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', width: 150 },
  { title: '资产权属公司', dataIndex: 'ownerCompany_dictText', width: 150 },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 150 }
]

// 点击输入框
const handleClick = () => {
  visible.value = true
  onTableChange()
  if (props.modelValue) {
    selectedRowKeys.value = [props.modelValue]
  }
}

// 清除选择
const handleClear = (e) => {
  e.stopPropagation()
  emit('update:modelValue', undefined)
  emit('update:displayValue', undefined)
  emit('change', undefined)
  clearSelection()
}

// 确认选择
const handleConfirm = () => {
  if (selectedRows.value?.[0]) {
    const selectedRow = selectedRows.value[0]
    emit('update:displayValue', selectedRow.name)
    emit('change', selectedRow)
    visible.value = false
  } else {
    message.warning('请选择一条记录')
  }
}

// 取消选择
const handleCancel = () => {
  visible.value = false
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      current: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

// 表格分页变化
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}
</script>

<style scoped>
.house-owner-selector {
  display: inline-block;
}

.selector-input {
  cursor: pointer;
  background-color: #fff;
}

.selector-input :deep(.ant-input) {
  cursor: pointer;
}

.selector-input :deep(.ant-input-suffix) {
  display: flex;
  align-items: center;
}

.selector-input :deep(.a-icon-arrow-down) {
  color: rgba(0, 0, 0, 0.25);
  font-size: 12px;
  margin-right: 6px;
  transition: transform 0.3s;
}

.selector-input:hover :deep(.a-icon-arrow-down) {
  color: #000;
}
</style>
