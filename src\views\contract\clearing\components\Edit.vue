<template>
  <a-drawer
    v-model:open="visible"
    class="edit-file-archiving-drawer common-drawer"
    title="合同退租清算申请"
    placement="right"
    width="1000px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <h2 class="text-[16px] font-bold mb-[20px]">基础信息</h2>
      <a-form :model="form" :rules="rules" ref="formRef" :label-col="{ style: { width: '84px' } }" autocomplete="off">
        <a-form-item label="业务时间" name="bizDate">
          <a-date-picker v-model:value="form.bizDate" value-format="YYYY-MM-DD" style="width: 100%"></a-date-picker>
        </a-form-item>
        <a-form-item label="经办人" name="handler">
          <user-select v-model="form.handler"></user-select>
        </a-form-item>
        <a-form-item label="计费截止日期" name="closingDate">
          <a-date-picker v-model:value="form.closingDate" value-format="YYYY-MM-DD" style="width: 100%"></a-date-picker>
        </a-form-item>
        <a-form-item label="合同" name="contract">
          <api-select
            v-model="form.contract"
            :async-fn="getContractList"
            width="376px"
            :field-names="{ label: 'number', value: 'id' }"
            @change="loadContractDetail"
          ></api-select>
          <div class="flex flex-wrap gap-[12px] mt-[20px] text-secondary" v-if="contractDetail.id">
            <span class="w-[calc(50%-6px)]">签约客户: {{ contractDetail.customer_dictText }}</span>
            <span class="w-[calc(50%-6px)]">签约日期: {{ contractDetail.startDate }}</span>
            <span class="w-[calc(50%-6px)]">管理公司: {{ contractDetail.manageCompany_dictText }}</span>
            <span class="w-[calc(50%-6px)]">合同类型: {{ contractDetail.contractType_dictText }}</span>
            <span class="w-[calc(50%-6px)]">业务员: {{ contractDetail.operator_dictText }}</span>
            <span class="w-[calc(50%-6px)]">租赁单元: {{ contractDetail.customer }}</span>
            <span class="w-[calc(50%-6px)]">合同开始日期: {{ contractDetail.startDate }}</span>
            <span class="w-[calc(50%-6px)]">合同结束日期: {{ contractDetail.expireDate }}</span>
          </div>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="form.remark"
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 5, maxRows: 5 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
      <div class="flex items-center justify-between mt-[40px] mb-[16px]">
        <h4 class="text-[16px] font-bold">清算明细</h4>
        <a-button type="primary">
          <i class="a-icon-plus"></i>
          添加明细
        </a-button>
      </div>
      <a-checkbox v-model:checked="form.isLiquidatedDamages">存在违约</a-checkbox>
      <a-form :model="form" ref="form2Ref" :rules="rules">
        <a-form-item label="违约金额" name="liquidatedDamagesAmount">
          <a-input v-model:value="form.liquidatedDamagesAmount">
            <template #suffix>元</template>
          </a-input>
        </a-form-item>
        <a-form-item label="违约说明" name="liquidatedDamagesRemark">
          <a-textarea
            v-model:value="form.liquidatedDamagesRemark"
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 5, maxRows: 5 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button type="primary" :loading="saveLoading" ghost @click="handleSave">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { edit, submit, detail, save } from '../apis.js'
import { list as getContractList, detail as getContractDetail } from '@/views/contract/management/apis'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  for (const key in form) {
    form[key] = result[key]
  }
  loading.value = false
}

const form = reactive({
  id: '',
  number: '',
  manageCompany: '',
  bizStatus: '',
  status: '',
  closingDate: '',
  handler: '',
  handlerDepart: '',
  clearingType: '',
  totalAmount: '',
  totalReceivable: '',
  totalDeductible: '',
  contract: '',
  signDate: '',
  customer: '',
  contractType: '',
  operator: '',
  operatorDepart: '',
  startDate: '',
  expireDate: '',
  leaseUnit: '',
  isLiquidatedDamages: true,
  liquidatedDamagesAmount: '',
  liquidatedDamagesRemark: '',
  actualDealAmount: '',
  remark: ''
})

const rules = {
  bizDate: [{ required: true, message: '请选择业务时间', trigger: 'change' }],
  contract: [{ required: true, message: '请选择合同', trigger: 'change' }]
}

const contractDetail = reactive({})
const loadContractDetail = async (id) => {
  const { result } = await getContractDetail({ id })
  Object.assign(contractDetail, result)
}

// 暂存
const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  try {
    saveLoading.value = true
    form.id ? await edit(getParams()) : await save(getParams())
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const getParams = () => {
  return {
    id: form.id,
    number: form.number,
    bizDate: form.bizDate,
    fileFillDepart: form.fileFillDepart,
    fileFillType: form.fileFillType,
    status: form.status,
    contract: form.contract,
    signDate: form.signDate,
    customer: form.customer,
    contractType: form.contractType,
    operator: form.operator,
    operatorDepart: form.operatorDepart,
    startDate: form.startDate,
    expireDate: form.expireDate,
    leaseUnit: form.leaseUnit,
    remark: form.remark,
    ctrlUnit: form.ctrlUnit,
    dataFileFillDetailEntryList: form.dataFileFillDetailEntryList
  }
}

const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  try {
    confirmLoading.value = true
    await submit(getParams())
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  for (const key in form) {
    if (Array.isArray(form[key])) {
      form[key] = []
    } else {
      form[key] = ''
    }
  }
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-file-archiving-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
  }
  .ant-form-item {
    width: 50%;
    &:last-child {
      width: 100%;
    }
  }
}
</style>
