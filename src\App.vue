<template>
  <a-config-provider :theme="theme" :locale="zhCN" component-size="large">
    <router-view></router-view>
  </a-config-provider>
</template>

<script setup>
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import { useUserStore } from '@/store/modules/user'
import { useDictStore } from '@/store/modules/dict'

const userStore = useUserStore()
const dictStore = useDictStore()
if (userStore.token) {
  userStore.getPermissions()
  dictStore.getAllDict()
}

const theme = {
  token: {
    colorPrimary: '#1D64F0',
    colorSuccess: '#6EC21B',
    colorError: '#F03A1D',
    colorWarning: '#FAB700',
    colorInfo: '#D7DAE0',
    colorText: '#172B52',
    colorTextBase: '#172B52',
    colorTextSecondary: '#495A7A',
    colorTextTertiary: '#8992A3',
    borderRadius: 8,
    fontFamily: 'siyuan',
    fontSizeLG: 14,
    borderRadiusLG: 8
  }
}
</script>
