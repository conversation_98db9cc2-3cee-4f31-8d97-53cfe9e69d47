<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="ruleForm.id ? '编辑资产跟踪' : '新增资产跟踪'"
    placement="right"
    width="1200px"
    @close="handleCancel"
  >
    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '150px' } }"
      autocomplete="off"
    >
      <div class="mb-[12px] mt-[12px] flex items-center">
        <div class="text-[16px] font-bold">跟踪类型</div>
        <a-radio-group
          class="!m-0 !ml-[86px]"
          v-model:value="curTrackingType"
          style="margin: 8px"
          @change="activeTabChange"
        >
          <a-radio-button v-for="item in trackingTypeDic" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-radio-button>
        </a-radio-group>
      </div>

      <div class="text-[16px] font-bold mb-[12px] mt-[12px]">跟踪信息</div>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="资产" name="houseOwner">
            <assets-select
              v-model="ruleForm.houseOwner"
              :options="[{ value: ruleForm.houseOwner, label: ruleForm.houseOwner_dictText }]"
              placeholder="请选择资产"
            ></assets-select>
          </a-form-item>
        </a-col>

        <!-- 闲置 -->
        <template v-if="curTrackingType === 'Idle'">
          <a-col :span="12">
            <a-form-item label="闲置面积" name="unUsedArea">
              <a-input-number
                allow-clear
                v-model:value="ruleForm.unUsedArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入闲置面积"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="闲置状态开始日期" name="unUsedBeginDate">
              <a-date-picker
                allow-clear
                v-model:value="ruleForm.unUsedBeginDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择闲置状态开始日期"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="闲置原因" name="unUsedReason">
              <a-input v-model:value="ruleForm.unUsedReason" placeholder="请输入闲置原因" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="闲置时间" name="unUsedTime">
              <a-date-picker
                allow-clear
                v-model:value="ruleForm.unUsedTime"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择闲置时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </template>
        <!-- 占用 -->
        <template v-if="curTrackingType === 'Occupy'">
          <a-col :span="12">
            <a-form-item label="被占用面积" name="occupyArea">
              <a-input-number
                allow-clear
                v-model:value="ruleForm.occupyArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入被占用面积"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="占用人" name="occupyPerson">
              <a-input
                allow-clear
                v-model:value="ruleForm.occupyPerson"
                style="width: 100%"
                placeholder="请输入占用人"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="占用原因" name="occupyReason">
              <a-input v-model:value="ruleForm.occupyReason" placeholder="请输入占用原因" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="被占用开始日期" name="occupyBeginDate">
              <a-date-picker
                allow-clear
                v-model:value="ruleForm.occupyBeginDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择被占用开始日期"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="其他情况" name="otherSituations">
              <a-checkbox v-model:checked="ruleForm.changeUse">改变用途</a-checkbox>
              <a-checkbox v-model:checked="ruleForm.illegal">违建</a-checkbox>
            </a-form-item>
          </a-col>
        </template>

        <!-- 借用 -->
        <template v-if="curTrackingType === 'Borrow'">
          <a-col :span="12">
            <a-form-item label="被借用面积" name="borrowArea">
              <a-input-number
                allow-clear
                v-model:value="ruleForm.borrowArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入被借用面积"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="借用人" name="borrowPerson">
              <a-input v-model:value="ruleForm.borrowPerson" style="width: 100%" placeholder="请输入借用人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="借用原因" name="borrowReason">
              <a-input v-model:value="ruleForm.borrowReason" placeholder="请输入借用原因" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="被借用开始日期" name="borrowBeginDate">
              <a-date-picker
                allow-clear
                v-model:value="ruleForm.borrowBeginDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择被借用开始日期"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="其他情况" name="changeUse">
              <a-checkbox v-model:checked="ruleForm.changeUse">改变用途</a-checkbox>
            </a-form-item>
          </a-col>
        </template>

        <!-- 自用 -->
        <template v-if="curTrackingType === 'Self'">
          <a-col :span="12">
            <a-form-item label="自用面积" name="selfArea">
              <a-input-number
                allow-clear
                v-model:value="ruleForm.selfArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入自用面积"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="自用原因" name="selfReason">
              <a-input v-model:value="ruleForm.selfReason" placeholder="请输入自用原因" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="被自用开始日期" name="selfBeginDate">
              <a-date-picker
                allow-clear
                v-model:value="ruleForm.selfBeginDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择被自用开始日期"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="其他情况" name="otherSituations">
              <a-checkbox v-model:checked="ruleForm.profitable">有收益</a-checkbox>
              <a-checkbox v-model:checked="ruleForm.devStandards">制定办公用房标准</a-checkbox>
              <a-checkbox v-model:checked="ruleForm.outLimit">超标</a-checkbox>
            </a-form-item>
          </a-col>
        </template>

        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="ruleForm.remark" placeholder="请输入备注" :maxlength="500" show-count />
          </a-form-item>
        </a-col>
      </a-row>

      <div class="mb-[12px] mt-[12px] flex justify-between" v-if="['Idle', 'Occupy'].includes(curTrackingType)">
        <div class="text-[16px] font-bold">盘活记录</div>
        <a-button type="primary" ghost @click="handleAddIdleAssetActivateList">
          <span class="a-icon-plus mr-[8px]"></span>
          增加记录
        </a-button>
      </div>
      <!-- 闲置 -->
      <draggable
        v-if="curTrackingType === 'Idle'"
        v-model="ruleForm.idleAssetActivateList"
        handle=".a-icon-move"
        item-key="id"
      >
        <template #item="{ element, index }">
          <a-row :gutter="24">
            <a-col :span="3">
              <a-form-item :label="index ? '' : '操作'" name="" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                <i
                  class="a-icon-move cursor-move text-secondary mr-[10px] text-[18px]"
                  v-if="showMoveBtn(element, index)"
                ></i>
                <i class="a-icon-remove cursor-hover text-secondary text-[18px]" @click="warningVisible = true"></i>
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item
                :label="index ? '' : '盘活措施填报日期'"
                :name="['idleAssetActivateList', index, 'fillDate']"
                :label-col="{ span: 24 }"
                :wrapper-col="{ span: 24 }"
                :rules="{
                  required: true,
                  message: '请选择盘活措施填报日期',
                  trigger: 'change'
                }"
              >
                <a-date-picker
                  allow-clear
                  v-model:value="element.fillDate"
                  picker="date"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择闲置时间"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item
                :label="index ? '' : '已采取的盘活管理措施'"
                :name="['idleAssetActivateList', index, 'ctrlMeasure']"
                :label-col="{ span: 24 }"
                :wrapper-col="{ span: 24 }"
                :rules="{
                  required: true,
                  message: '请输入已采取的盘活管理措施',
                  trigger: 'change'
                }"
              >
                <a-input
                  v-model:value="element.ctrlMeasure"
                  placeholder="请输入已采取的盘活管理措施"
                  allow-clear
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item
                :label="index ? '' : '下一个盘活建议'"
                :name="['idleAssetActivateList', index, 'activateAdvise']"
                :label-col="{ span: 24 }"
                :wrapper-col="{ span: 24 }"
                :rules="{
                  required: true,
                  message: '请输入下一个盘活建议',
                  trigger: 'change'
                }"
              >
                <a-input
                  v-model:value="element.activateAdvise"
                  placeholder="请输入下一步盘活建议"
                  allow-clear
                ></a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </template>
      </draggable>
      <draggable
        v-if="curTrackingType === 'Occupy'"
        v-model="ruleForm.occupyAssetActivateList"
        handle=".a-icon-move"
        item-key="id"
      >
        <template #item="{ element, index }">
          <a-row :gutter="24">
            <a-col :span="3">
              <a-form-item :label="index ? '' : '操作'" name="" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                <i
                  class="a-icon-move cursor-move text-secondary mr-[10px] text-[18px]"
                  v-if="showMoveBtn(element, index)"
                ></i>
                <i class="a-icon-remove cursor-hover text-secondary text-[18px]" @click="warningVisible = true"></i>
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item
                :label="index ? '' : '盘活措施填报日期'"
                :name="['occupyAssetActivateList', index, 'fillDate']"
                :label-col="{ span: 24 }"
                :wrapper-col="{ span: 24 }"
                :rules="{
                  required: true,
                  message: '请选择盘活措施填报日期',
                  trigger: 'change'
                }"
              >
                <a-date-picker
                  allow-clear
                  v-model:value="element.fillDate"
                  picker="date"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择闲置时间"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item
                :label="index ? '' : '已采取的盘活管理措施'"
                :name="['occupyAssetActivateList', index, 'ctrlMeasure']"
                :label-col="{ span: 24 }"
                :wrapper-col="{ span: 24 }"
                :rules="{
                  required: true,
                  message: '请输入已采取的盘活管理措施',
                  trigger: 'change'
                }"
              >
                <a-input
                  v-model:value="element.ctrlMeasure"
                  placeholder="请输入已采取的盘活管理措施"
                  allow-clear
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item
                :label="index ? '' : '下一个盘活建议'"
                :name="['occupyAssetActivateList', index, 'activateAdvise']"
                :label-col="{ span: 24 }"
                :wrapper-col="{ span: 24 }"
                :rules="{
                  required: true,
                  message: '请输入下一个盘活建议',
                  trigger: 'change'
                }"
              >
                <a-input
                  v-model:value="element.activateAdvise"
                  placeholder="请输入下一步盘活建议"
                  allow-clear
                ></a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </template>
      </draggable>
    </a-form>

    <template #footer>
      <a-button type="primary" @click="handleConfirm">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>

    <a-modal v-model:open="warningVisible" title="确定删除盘活记录吗？" @ok="handleDel" :closable="false"></a-modal>
  </a-drawer>
</template>
<script setup>
import {
  idleAdd,
  occupyAdd,
  borrowAdd,
  selfAdd,
  idleSubmit,
  occupySubmit,
  borrowSubmit,
  selfSubmit,
  idleEdit,
  occupyEdit,
  borrowEdit,
  selfEdit
} from '../apis.js'
import { useDictStore } from '@/store/modules/dict'
import { message } from 'ant-design-vue'
import draggable from 'vuedraggable'
// import { getProjectList } from '../../../leaseUnit/manage/apis'
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data) => {
  if (data) {
    curTrackingType.value = data.trackingType
    Object.assign(ruleForm.value, data)
  }
  visible.value = true
  // loadMenuList()
}
defineExpose({ open })
const curTrackingType = ref('Borrow')
const store = useDictStore()
const trackingTypeDic = computed(() => {
  if (!store.dict) return []
  if (!Object.keys(store.dict).length) return []
  return store.dict.CT_BASE_ENUM_TrackingType
})
// Borrow:借用 Occupy：占用；Idle：闲置 Self：自用
const requestFunc = computed(() => {
  if (curTrackingType.value === 'Idle') {
    return {
      add: idleAdd,
      submit: idleSubmit,
      edit: idleEdit
    }
  }
  if (curTrackingType.value === 'Occupy') {
    return {
      add: occupyAdd,
      submit: occupySubmit,
      edit: occupyEdit
    }
  }
  if (curTrackingType.value === 'Borrow') {
    return {
      add: borrowAdd,
      submit: borrowSubmit,
      edit: borrowEdit
    }
  }
  return {
    add: selfAdd,
    submit: selfSubmit,
    edit: selfEdit
  }
})

const ruleForm = ref({
  id: '',
  number: '',
  manageCompany: '',
  bizDate: '',
  trackingType: '',
  status: '',
  houseOwner: '',
  // 自用参数
  selfArea: '',
  selfReason: '',
  selfBeginDate: '',
  profitable: '',
  devStandards: '',
  outLimit: '',
  // 闲置
  unUsedBeginDate: '',
  unUsedEndDate: '',
  unUsedArea: '',
  unUsedTime: '',
  unUsedReason: '',

  sourceBillId: '',
  sourceBillEntryId: '',
  occupyArea: undefined,
  occupyPerson: '',
  occupyReason: '',
  occupyBeginDate: '',
  occupyEndDate: '',
  changeUse: false,
  illegal: false,
  remark: '',
  idleAssetActivateList: [],
  occupyAssetActivateList: [],
  borrowAssetTrackingInfoList: [],
  selfAssetTrackingInfoList: []
})

const submitRuleForm = computed(() => {
  if (curTrackingType.value === 'Idle') {
    return {
      trackingType: curTrackingType.value,
      id: ruleForm.value.id,
      houseOwner: ruleForm.value.houseOwner,
      unUsedArea: ruleForm.value.unUsedArea,
      unUsedBeginDate: ruleForm.value.unUsedBeginDate,
      unUsedReason: ruleForm.value.unUsedReason,
      unUsedTime: ruleForm.value.unUsedTime,
      remark: ruleForm.value.remark,
      idleAssetActivateList: ruleForm.value.idleAssetActivateList
    }
  }
  if (curTrackingType.value === 'Occupy') {
    return {
      trackingType: curTrackingType.value,
      id: ruleForm.value.id,
      houseOwner: ruleForm.value.houseOwner,
      occupyArea: ruleForm.value.occupyArea,
      occupyPerson: ruleForm.value.occupyPerson,
      occupyReason: ruleForm.value.occupyReason,
      occupyBeginDate: ruleForm.value.occupyBeginDate,
      changeUse: ruleForm.value.changeUse,
      illegal: ruleForm.value.illegal,
      remark: ruleForm.value.remark,
      occupyAssetActivateList: ruleForm.value.occupyAssetActivateList
    }
  }
  if (curTrackingType.value === 'Borrow') {
    return {
      trackingType: curTrackingType.value,
      id: ruleForm.value.id,
      houseOwner: ruleForm.value.houseOwner,
      borrowArea: ruleForm.value.borrowArea,
      borrowPerson: ruleForm.value.borrowPerson,
      borrowReason: ruleForm.value.borrowReason,
      borrowBeginDate: ruleForm.value.borrowBeginDate,
      otherSituations: ruleForm.value.otherSituations,
      remark: ruleForm.value.remark,
      borrowAssetTrackingInfoList: ruleForm.value.borrowAssetTrackingInfoList
    }
  }
  return {
    trackingType: curTrackingType.value,
    id: ruleForm.value.id,
    houseOwner: ruleForm.value.houseOwner,
    selfArea: ruleForm.value.selfArea,
    selfReason: ruleForm.value.selfReason,
    selfBeginDate: ruleForm.value.selfBeginDate,
    profitable: ruleForm.value.profitable,
    devStandards: ruleForm.value.devStandards,
    outLimit: ruleForm.value.outLimit,
    remark: ruleForm.value.remark,
    selfAssetTrackingInfoList: ruleForm.value.selfAssetTrackingInfoList
  }
})

const rules = computed(() => {
  if (curTrackingType.value === 'Idle') {
    return {
      houseOwner: [{ required: true, message: '请选择资产', trigger: ['change'] }],
      unUsedArea: [{ required: true, message: '请输入闲置面积', trigger: ['blur'] }],
      unUsedBeginDate: [{ required: true, message: '请选择闲置状态开始日期', trigger: ['change'] }],
      unUsedReason: [{ required: true, message: '请输入闲置原因', trigger: ['blur'] }]
      // idleAssetActivateList: [{ type: 'array', required: true, message: '请输入盘活记录', trigger: ['blur', 'change'] }]
    }
  }
  if (curTrackingType.value === 'Occupy') {
    return {
      houseOwner: [{ required: true, message: '请选择资产', trigger: ['change'] }],
      occupyArea: [{ required: true, message: '请输入被占用面积', trigger: ['blur'] }],
      occupyPerson: [{ required: true, message: '请输入占用人', trigger: ['blur'] }],
      occupyReason: [{ required: true, message: '请输入占用原因', trigger: ['blur'] }],
      occupyBeginDate: [{ required: true, message: '请选择被占用开始日期', trigger: ['change'] }]
    }
  }
  if (curTrackingType.value === 'Borrow') {
    return {
      houseOwner: [{ required: true, message: '请选择资产', trigger: ['change'] }],
      borrowArea: [{ required: true, message: '请输入被借用面积', trigger: ['blur'] }],
      borrowPerson: [{ required: true, message: '请输入借用人', trigger: ['blur'] }],
      borrowReason: [{ required: true, message: '请输入借用原因', trigger: ['blur'] }],
      borrowBeginDate: [{ required: true, message: '请选择被借用开始日期', trigger: ['change'] }]
    }
  }
  return {
    houseOwner: [{ required: true, message: '请选择资产', trigger: ['change'] }],
    selfArea: [{ required: true, message: '请输入自用面积', trigger: ['blur'] }],
    selfReason: [{ required: true, message: '请输入自用原因', trigger: ['blur'] }],
    selfBeginDate: [{ required: true, message: '请选择被自用开始日期', trigger: ['change'] }]
  }
})
const activeTabChange = () => {
  // loadData()
}

const handleAddIdleAssetActivateList = () => {
  switch (curTrackingType.value) {
    case 'Idle':
      ruleForm.value.idleAssetActivateList.push({
        id: '',
        fillDate: '',
        ctrlMeasure: '',
        activateAdvise: '',
        seq: undefined,
        parent: '',
        remark: '',
        delFlag: undefined
      })
      break
    case 'Occupy':
      ruleForm.value.occupyAssetActivateList.push({
        id: '',
        fillDate: '',
        ctrlMeasure: '',
        activateAdvise: '',
        seq: undefined,
        parent: '',
        remark: '',
        delFlag: undefined
      })
      break
    case 'Borrow':
      ruleForm.value.borrowAssetTrackingInfoList.push({
        id: '',
        fillDate: '',
        ctrlMeasure: '',
        activateAdvise: '',
        seq: undefined,
        parent: '',
        remark: '',
        delFlag: undefined
      })
      break
    case 'Self':
      ruleForm.value.selfAssetTrackingInfoList.push({
        id: '',
        fillDate: '',
        ctrlMeasure: '',
        activateAdvise: '',
        seq: undefined,
        parent: '',
        remark: '',
        delFlag: undefined
      })
      break
  }
}
const showMoveBtn = () => {
  let boolean = false
  switch (curTrackingType.value) {
    case 'Idle':
      boolean = ruleForm.value.idleAssetActivateList.length > 1
      break
    case 'Occupy':
      boolean = ruleForm.value.occupyAssetActivateList.length > 1
      break
    case 'Borrow':
      boolean = ruleForm.value.borrowAssetTrackingInfoList.length > 1
      break
    case 'Self':
      boolean = ruleForm.value.selfAssetTrackingInfoList.length > 1
      break
  }

  return boolean
}
const warningVisible = ref(false)
const handleDel = (element, index) => {
  switch (curTrackingType.value) {
    case 'Idle':
      ruleForm.value.idleAssetActivateList.splice(index, 1)
      break
    case 'Occupy':
      ruleForm.value.occupyAssetActivateList.splice(index, 1)
      break
    case 'Borrow':
      ruleForm.value.borrowAssetTrackingInfoList.splice(index, 1)
      break
    case 'Self':
      ruleForm.value.selfAssetTrackingInfoList.splice(index, 1)
      break
  }
  warningVisible.value = false
}

// // 资产表单项
// const assetsSelectRef = ref()
// const handleHouseOwnerClick = (event) => {
//   event.preventDefault()
//   assetsSelectRef.value.open()
// }
// 提交
const formRef = ref()
const handleConfirm = async () => {
  await formRef.value.validate()
  await requestFunc.value.submit({ ...submitRuleForm.value })
  message.success('提交成功')
}
// 取消
const handleCancel = () => {
  ruleForm.value = {
    id: '',
    number: '',
    manageCompany: '',
    bizDate: '',
    trackingType: '',
    status: '',
    houseOwner: '',
    unUsedBeginDate: '',
    unUsedEndDate: '',
    unUsedArea: '',
    unUsedTime: '',
    unUsedReason: '',
    remark: '',
    sourceBillId: '',
    sourceBillEntryId: '',
    occupyArea: undefined,
    occupyPerson: '',
    occupyReason: '',
    occupyBeginDate: '',
    occupyEndDate: '',
    changeUse: false,
    illegal: false,
    idleAssetActivateList: [],
    occupyAssetActivateList: [],
    borrowAssetTrackingInfoList: [],
    selfAssetTrackingInfoList: []
  }

  formRef.value.clearValidate()
  visible.value = false
}
</script>
