<template>
  <div class="w-full">
    <div class="flex items-stretch border border-gray-200 rounded-lg overflow-hidden">
      <div class="flex-1 flex flex-col min-h-[300px] bg-white source-list">
        <div class="flex justify-between items-center py-3 px-4 border-b border-gray-200">
          <div class="font-medium text-base">源单元</div>
          <a-button type="primary" size="small" @click="handleSelectSourceUnit">选择源单元</a-button>
        </div>
        <div class="flex-1 overflow-y-auto">
          <div v-if="sourceUnits.length === 0" class="flex items-center justify-center h-full min-h-[200px]">
            <a-empty description="暂无数据" />
          </div>
          <div v-else class="p-2">
            <div
              v-for="item in sourceUnits"
              :key="item.id"
              class="flex justify-between items-start p-3 mb-2 border border-gray-200 rounded hover:bg-gray-100 transition-all duration-300"
            >
              <div class="flex-1 overflow-hidden">
                <div class="font-medium mb-2 whitespace-nowrap overflow-hidden text-ellipsis">
                  {{ item.leaseUnitObject.name }} ({{ item.leaseUnitObject.number }})
                </div>
                <div class="text-xs text-gray-600">
                  <div class="flex mt-1">
                    <span class="flex-1">
                      地址：{{ item.leaseUnitObject.province }}{{ item.leaseUnitObject.city
                      }}{{ item.leaseUnitObject.district }}{{ item.leaseUnitObject.detailAddress }}
                    </span>
                    <span class="flex-1">租赁面积：{{ item.leaseUnitObject.leaseArea }}m²</span>
                  </div>
                  <div class="flex mt-1">
                    <span class="flex-1">产权：{{ item.leaseUnitObject.propertyUse_dictText }}</span>
                    <span class="flex-1">租赁用途：{{ item.leaseUnitObject.leaseUse_dictText }}</span>
                  </div>
                </div>
              </div>
              <div class="flex flex-col items-end">
                <a-button type="link" danger @click="removeSourceUnit(item)">删除</a-button>
                <a-button type="link" @click="viewSourceUnitDetail(item)">
                  <i class="a-icon-right"></i>
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-center w-[50px] bg-gray-100">
        <div class="w-6 h-6 flex items-center justify-center text-blue-500 text-base">
          <i class="a-icon-arrow-right"></i>
        </div>
      </div>

      <div class="flex-1 flex flex-col min-h-[300px] bg-white target-list">
        <div class="flex justify-between items-center py-3 px-4 border-b border-gray-200">
          <div class="font-medium text-base">新单元</div>
          <a-button type="primary" size="small" @click="handleAddTargetUnit">添加新单元</a-button>
        </div>
        <div class="flex-1 overflow-y-auto">
          <div v-if="targetUnits.length === 0" class="flex items-center justify-center h-full min-h-[200px]">
            <a-empty description="暂无数据" />
          </div>
          <div v-else class="p-2">
            <div
              v-for="(item, index) in targetUnits"
              :key="item.id || index"
              class="flex justify-between items-start p-3 mb-2 border border-gray-200 rounded hover:bg-gray-100 transition-all duration-300"
            >
              <div class="flex-1 overflow-hidden">
                <div class="font-medium mb-2 whitespace-nowrap overflow-hidden text-ellipsis">
                  {{ item.leaseUnitObject.name }} ({{ item.leaseUnitObject.number }})
                </div>
                <div class="text-xs text-gray-600">
                  <div class="flex mt-1">
                    <span class="flex-1">
                      地址：{{ item.leaseUnitObject.province }}{{ item.leaseUnitObject.city
                      }}{{ item.leaseUnitObject.district }}{{ item.leaseUnitObject.detailAddress }}
                    </span>
                    <span class="flex-1">租赁面积：{{ item.leaseUnitObject.leaseArea }}m²</span>
                  </div>
                  <div class="flex mt-1">
                    <span class="flex-1">产权：{{ item.leaseUnitObject.propertyUse_dictText }}</span>
                    <span class="flex-1">租赁用途：{{ item.leaseUnitObject.leaseUse_dictText }}</span>
                  </div>
                </div>
              </div>
              <div class="flex flex-col items-end">
                <a-button type="link" danger @click="removeTargetUnit(index)">
                  <i class="a-icon-delete"></i>
                </a-button>
                <a-button type="link" @click="editTargetUnit(item, index)">
                  <i class="a-icon-edit"></i>
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <select-source-unit-modal ref="selectSourceUnitModalRef" @change="handleSourceUnitChange" />

    <edit-lease-unit ref="editLeaseUnitRef" @refresh="handleEditLeaseUnitRefresh"></edit-lease-unit>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import EditLeaseUnit from '@/views/leaseUnit/manage/components/EditLeaseUnit.vue'
import SelectSourceUnitModal from './SelectSourceUnitModal.vue'

const props = defineProps({
  sourceUnits: {
    type: Array,
    default: () => []
  },
  targetUnits: {
    type: Array,
    default: () => []
  },
  splitMergeType: {
    type: String,
    default: 'split'
  }
})

const emit = defineEmits(['update:sourceUnits', 'update:targetUnits'])

const editingTargetIndex = ref(null)
const editLeaseUnitRef = ref()
const selectSourceUnitModalRef = ref()

/**
 * 选择源单元
 */
const handleSelectSourceUnit = () => {
  selectSourceUnitModalRef.value.open(props.sourceUnits)
}

/**
 * 源单元选择变更
 */
const handleSourceUnitChange = (selectedUnits) => {
  if (selectedUnits && selectedUnits.length > 0) {
    emit(
      'update:sourceUnits',
      selectedUnits.map((unit) => ({
        id: unit.id,
        leaseUnitObject: { ...unit }
      }))
    )
    message.success('已选择源租赁单元')
  }
}

/**
 * 移除源单元
 */
const removeSourceUnit = (record) => {
  const newSourceUnits = props.sourceUnits.filter((item) => item.id !== record.id)
  emit('update:sourceUnits', newSourceUnits)
  message.success('已移除源租赁单元')
}

/**
 * 查看源单元详情
 */
const viewSourceUnitDetail = (record) => {
  message.info(`查看租赁单元详情：${record.name}`)
}

/**
 * 添加目标单元
 */
const handleAddTargetUnit = () => {
  editingTargetIndex.value = null
  editLeaseUnitRef.value.open()
}

/**
 * 编辑目标单元
 */
const editTargetUnit = (record, index) => {
  editingTargetIndex.value = index
  editLeaseUnitRef.value.open(record.leaseUnitObject)
}

/**
 * 编辑租赁单元刷新
 */
const handleEditLeaseUnitRefresh = (data) => {
  if (!data) return

  const newTargetUnits = [...props.targetUnits]
  const editingIndex = editingTargetIndex.value

  if (editingIndex !== null && editingIndex >= 0 && editingIndex < newTargetUnits.length) {
    newTargetUnits[editingIndex] = {
      id: data.id,
      leaseUnitObject: { ...data }
    }
    message.success('目标租赁单元编辑成功')
  } else {
    newTargetUnits.push({
      id: data.id,
      leaseUnitObject: { ...data }
    })
    message.success('目标租赁单元添加成功')
  }

  emit('update:targetUnits', newTargetUnits)
  editingTargetIndex.value = null
}

/**
 * 移除目标单元
 */
const removeTargetUnit = (index) => {
  const newTargetUnits = [...props.targetUnits]
  newTargetUnits.splice(index, 1)
  emit('update:targetUnits', newTargetUnits)
  message.success('已移除目标租赁单元')
}
</script>

<style scoped></style>
