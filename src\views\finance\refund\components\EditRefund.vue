<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}退款申请`"
    class="common-drawer"
    placement="right"
    width="1000px"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }">
        <h4 class="text-[16px] font-bold mb-[20px]">基础信息</h4>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="业务时间" name="bizDate">
              <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="退款客户" name="customer">
              <customer-select v-model="formData.customer" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="经办人" name="operator">
              <user-select
                v-model="formData.operator"
                v-model:display-value="formData.operator_dictText"
                placeholder="请选择经办人"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="业务部门" name="operatorDepart">
              <dept-tree-select v-model="formData.operatorDepart" placeholder="请选择业务部门" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remark" :label-col="{ span: 2 }">
              <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>

        <h4 class="text-[16px] font-bold my-[24px] flex justify-between">
          退款明细
          <a-button type="primary" @click="handleAddRefundItem">添加明细</a-button>
        </h4>
        <a-table
          :columns="refundColumns"
          :data-source="formData.refundItems"
          :pagination="false"
          :scroll="{ x: 1200 }"
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button type="link" danger @click="handleRemoveRefundItem(record)">删除</a-button>
            </template>
          </template>
        </a-table>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave(true)">提交</a-button>
      <a-button @click="handleSave(false)">暂存</a-button>
      <a-button @click="handleClose">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addRefundReqBill, editRefundReqBill } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

// 表单默认值
const formDataDefault = {
  bizDate: undefined,
  customer: undefined,
  operator: undefined,
  operatorDepart: undefined,
  remark: undefined
}

const formData = reactive({ ...formDataDefault })

// 表单校验规则
const rules = {
  bizDate: [{ required: true, message: '请选择业务日期' }],
  customer: [{ required: true, message: '请选择退款客户' }],
  operator: [{ required: true, message: '请选择经办人' }],
  operatorDepart: [{ required: true, message: '请选择业务部门' }]
}

const refundColumns = [
  { title: '账单编号', dataIndex: 'receiptNo', width: 200, fixed: 'left' },
  { title: '合同', dataIndex: 'type', width: 120 },
  { title: '款项类型', dataIndex: 'transferAmount', width: 120 },
  { title: '归属年月', dataIndex: 'transferAmount', width: 120 },
  { title: '期数/总期数', dataIndex: 'transferAmount', width: 120 },
  { title: '应收日期', dataIndex: 'transferAmount', width: 120 },
  { title: '剩余可退', dataIndex: 'transferAmount', width: 120, fixed: 'right' },
  { title: '本次退款金额', dataIndex: 'transferAmount', width: 120, fixed: 'right' },
  { title: '退款备注', dataIndex: 'remark', fixed: 'right' }
]

const open = (record) => {
  visible.value = true
  if (record?.id) {
    Object.assign(formData, record)
  }
}

const handleSave = async () => {
  await formRef.value?.validate()
  confirmLoading.value = true
  try {
    const submitData = { ...formData }
    if (formData.id) {
      await editRefundReqBill(submitData)
    } else {
      await addRefundReqBill(submitData)
    }
    message.success('保存成功')
  } finally {
    confirmLoading.value = false
  }
  handleClose()
}

const handleClose = () => {
  visible.value = false
  Object.assign(formData, formDataDefault)
  emits('refresh')
}

// 添加退款明细
const handleAddRefundItem = () => {}

// 删除退款明细
const handleRemoveRefundItem = () => {}

defineExpose({
  open
})
</script>
