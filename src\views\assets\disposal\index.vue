<template>
  <div>
    <div class="flex justify-between">
      <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
      <a-button type="primary" ghost @click="handleAdd">
        <span class="a-icon-statistics mr-[8px]"></span>
        统计分析
      </a-button>
    </div>

    <div class="flex justify-between !my-[24px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-form autocomplete="off" layout="inline" class="!ml-[40px]">
          <a-form-item label="搜索">
            <s-input
              v-model="search.number"
              placeholder="搜索单据编号"
              class="ml-[10px] !w-[280px]"
              @input="handleInput"
            ></s-input>
          </a-form-item>
          <a-form-item>
            <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
          </a-form-item>
        </a-form>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <!--bordered  row-key="id" :row-selection="{ selectedRowKeys, onChange: onSelectChange }" -->
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="pagination"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowView(record)">查看</span>
          <span class="primary-btn" @click="rowEdit(record)">编辑</span>
          <!-- 待审批 -->
          <span class="primary-btn" v-if="record.status === 'AUDITING'" @click="rowEdit(record)">撤回</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <!--  审核不通过，审核通过，待审核 -->
                <a-menu-item v-if="['AUDITING', 'AUDITNO', 'AUDITOK'].includes(record.status)">
                  <span class="primary-btn" @click="rowViewApprove(record)">查看审批</span>
                </a-menu-item>
                <!-- 审核不通过 -->
                <a-menu-item v-if="['AUDITNO'].includes(record.status)">
                  <span class="primary-btn" @click="rowResubmit(record, 1)">重新提交</span>
                </a-menu-item>
                <!-- 暂存 -->
                <a-menu-item v-if="record.status === 'TEMP'">
                  <span class="primary-btn" @click="rowResubmit(record)">提交</span>
                </a-menu-item>
                <!-- 暂存 审核不通过 撤回 -->
                <a-menu-item v-if="['TEMP', 'AUDITNO', 'BACK'].includes(record.status)">
                  <span class="primary-btn" @click="rowDel(record)">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <detail ref="detailRef" @load-data="onTableChange" :ids="tableIds || []"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产处置单"
      :download-fn="() => exportExcel('资产处置单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import usePageTable from '@/hooks/usePageTable'
import { renderDict, renderDictTag } from '@/utils/render'
import AddEdit from './components/AddEdit.vue'
import Detail from './components/Detail.vue'
// import useTableSelection from '@/hooks/useTableSelection'
import { getPage, submit, delById, exportExcel, importExcel } from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
  // 资产详情跳转过来新增
  if (route.query.adding) {
    addEditRef?.value.open()
  }
})
// const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const tableIds = computed(() => {
  return list.value.map((item) => item.id)
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}
const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const search = ref({
  number: '',
  manageCompany: '',
  assetsSource: '',
  bizDate: '',
  houseDealDate: '',
  transferMethod: '',
  transferor: '',
  decision: '',
  status: ''
  // number:"",
})
const searchList = reactive([
  // { label: '单据编号', name: 'number', type: 'input', placeholder: '请输入单据编号' },
  { label: '管理公司', name: 'manageCompany', type: 'deptTree', placeholder: '请选择管理公司' },
  { label: '资产来源', name: 'assetsSource', type: 'dic', code: 'CT_BAS_AssetsSource', placeholder: '请选择资产来源' },
  { label: '业务日期', name: 'bizDate', type: 'date', placeholder: '请选择业务日期' },
  { label: '房产处置日期', name: 'houseDealDate', type: 'date', placeholder: '请选择房产处置日期' },
  {
    label: '转让方式',
    name: 'transferMethod',
    type: 'dic',
    code: 'CT_BAS_TransferMethod',
    placeholder: '请选择转让方式'
  },
  { label: '转让方', name: 'transferor', type: 'deptTree', placeholder: '请选择转让方' },
  {
    label: '内部决议情况',
    name: 'decision',
    type: 'dic',
    code: 'CT_BASE_ENUM_HouseDealBill_Decision',
    placeholder: '请选择内部决议情况'
  },
  { label: '数据状态', name: 'status', type: 'dic', code: 'CT_BASE_ENUM_AuditStatus', placeholder: '请选择数据状态' }
  // { label: '资产编号', name: 'number' }
])

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: true },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 150 },
  { title: '资产来源', dataIndex: 'assetsSource', customRender: ({ text }) => renderDict(text, 'CT_BAS_AssetsSource') },
  { title: '业务日期', dataIndex: 'bizDate' },
  { title: '处置说明', dataIndex: 'dealExplain' },
  { title: '房产处置日期', dataIndex: 'houseDealDate' },
  {
    title: '转让方式',
    dataIndex: 'transferMethod',
    customRender: ({ text }) => renderDict(text, 'CT_BAS_TransferMethod')
  },
  { title: '转让方', dataIndex: 'transferor_dictText' },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    // customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_AuditStatus')
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '审核时间', dataIndex: 'auditTime' },
  { title: '审核人', dataIndex: 'auditBy' },

  { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)
const addEditRef = ref()
// 新增
const handleAdd = () => {
  addEditRef?.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 编辑
const rowEdit = (row) => {
  addEditRef?.value.open(row)
}

const rowViewApprove = () => {}
// 提交（重新提交）
const rowResubmit = (row, type = 0) => {
  Modal.confirm({
    title: '提示',
    content: type ? '确认重新提交？' : '确认提交？',
    centered: true,
    onOk: async () => {
      await submit(row)
      message.success(type ? '重新提交成功' : '提交成功')
      const pageNo = pagination.value.current
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}
const rowDel = ({ id }) => {
  Modal.confirm({
    title: '提示',
    content: '确认删除当前资产处置？',
    centered: true,
    onOk: async () => {
      await delById(id)
      message.success('删除成功')
      const pageNo = pagination.value.current
      // if (
      //   pageNo > 1 &&
      //   ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      // ) {
      //   pageNo--
      // }
      // clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('资产处置数据导出.xls', search)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}
</script>
