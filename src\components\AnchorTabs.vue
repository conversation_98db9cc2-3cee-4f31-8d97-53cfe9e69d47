<!-- 导航锚点tabs -->
<template>
  <div id="anchor-tabs">
    <div class="h-[36px] flex border-0 border-b border-solid border-[#e6e9f0] mb-[24px]">
      <span
        v-for="(item, index) in tabList"
        :key="item.name"
        class="anchor-item"
        :class="{ active: item.name === current }"
        @click="handleTitleClick(item, index)"
      >
        {{ item.title }}
      </span>
    </div>
    <div class="overflow-y-auto scrollbar" :style="{ height }">
      <section v-for="item in tabList" :key="item.name" class="section pb-[40px] last-of-type:pb-[0]">
        <slot :name="`${item.name}-title`" v-if="item.showTitle !== false">
          <h4 class="text-[16px] font-bold mb-[12px]">{{ item.title }}</h4>
        </slot>
        <slot :name="item.name"></slot>
      </section>
    </div>
  </div>
</template>

<script setup>
const { tabList } = defineProps({
  // 数据格式: { title: string, name: string, showTitle?: boolean }[]，其中name为插槽名称
  tabList: {
    required: true,
    type: Array,
    validator: (val) => {
      const result = val.every((item) => {
        return typeof item.title === 'string' && typeof item.name === 'string'
      })
      if (!result) throw new Error('请传入{ title: string, name: string, showTitle?: boolean }[]格式的数据')
      return true
    }
  },
  height: { type: String, default: '50vh' } // 设置滚动容器的高度
})

const current = ref(tabList[0].name)

let isClickTab = false // 标记，容器的滚动是否是由点击tab触发
const handleTitleClick = (item, index) => {
  current.value = item.name
  anchorTabs.querySelectorAll('.section')[index].scrollIntoView({ behavior: 'smooth', block: 'start' })
  isClickTab = true
}

let timer
// 监听页面滚动
const watchScroll = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    // 如果滚动行为是由于点击tab带来的，则不做处理
    if (isClickTab) {
      isClickTab = false
      return
    }
    const result = sectionList.map((item) => isVisible(item))
    const index = result.lastIndexOf(true)
    current.value = tabList[index].name
  }, 200)
}

/**
 * 判断元素是否在容器的可视区域内
 * 这里取容器正中间的60px作为判断标准，哪个section与容器正中间的60px相交，则哪个section就是当前激活的section
 * @param {HTMLElement} el 每一个section
 * @return {boolean}
 */
const isVisible = (el) => {
  const elRect = el.getBoundingClientRect()
  // 取容器正中间的60px作为区间
  const diff = parseInt((scrollBoxRect.bottom - scrollBoxRect.top - 60) / 2)
  const start = scrollBoxRect.top + diff
  const end = start + 60
  return elRect.top <= end && elRect.bottom >= start
}

let anchorTabs // 组件最外层类名
let scrollBox // 滚动容器

// scrollBox的几何信息
const scrollBoxRect = {
  top: 0,
  bottom: 0
}
let sectionList // section元素列表

onMounted(() => {
  requestAnimationFrame(() => {
    anchorTabs = document.querySelector('#anchor-tabs')
    scrollBox = anchorTabs.querySelector('.scrollbar')
    const rect = scrollBox.getBoundingClientRect()
    scrollBoxRect.top = rect.top
    scrollBoxRect.bottom = rect.bottom
    sectionList = Array.from(scrollBox.querySelectorAll('.section'))
    scrollBox.addEventListener('scroll', watchScroll)
  })
})

onUnmounted(() => {
  scrollBox.removeEventListener('scroll', watchScroll)
})
</script>

<style lang="less" scoped>
.anchor-item {
  height: 100%;
  border-bottom: 2px solid transparent;
  transition:
    color 0.2s,
    border-color 0.2s,
    background 0.2s;
  cursor: pointer;
  font-size: 16px;
  color: var(--color-secondary);
  margin-right: 24px;
  user-select: none;
  &:hover {
    color: var(--color-primary);
  }
  &.active {
    border-color: var(--color-primary);
    color: var(--color-primary);
    background: linear-gradient(180deg, rgba(242, 246, 254, 0), #f2f6fe 100%);
  }
}
</style>
