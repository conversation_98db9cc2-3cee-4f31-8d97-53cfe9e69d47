<template>
  <a-drawer v-model:open="visible" :mask-closable="false" class="common-detail-drawer" placement="right" width="1200px">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === 0 }"
            @click="handleSwitch(1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === ids.length - 1 }"
            @click="handleSwitch()"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px] mr-[28px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn">查看审核记录</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn">反审核</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="handleDel">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">资产详情：{{ detailData.name }}</h2>
        <a-tag :type="getStatusColor(detailData.status)">
          {{ detailData.status_dictText }}
        </a-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>

      <div>
        <div class="text-[16px] font-bold mb-[12px]">跟踪信息</div>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">资产：{{ detailData.landPrice || '-' }}</span>
          <span class="w-[50%]">借用面积：{{ '-' }}</span>
          <span class="w-[50%]">借用人：{{ detailData.landRent || '-' }}</span>
          <span class="w-[50%]">借用原因：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">借用状态开始时间：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">借用状态结束时间：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">其他情况：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
      </div>
    </a-spin>
    <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>

    <template #footer>
      <a-button>结束</a-button>
    </template>
  </a-drawer>
</template>
<script setup>
import {
  idleDelById,
  occupyDelById,
  borrowDelById,
  selfDelById,
  idleQueryById,
  occupyQueryById,
  borrowQueryById,
  selfQueryById
} from '../apis'
import AddEdit from './AddEdit.vue'
import { Modal, message } from 'ant-design-vue'
const emits = defineEmits(['loadData'])
const { ids } = defineProps({
  ids: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const curIndex = ref(0)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  detailData.value.trackingType = data.trackingType
  visible.value = true
  if (data.id) {
    const activeIndex = ids.indexOf(data.id)
    curIndex.value = activeIndex === -1 ? 0 : activeIndex
    getDetailById(data.id)
  }
}
defineExpose({ open })
const detailData = ref({})
const loading = ref(false)
// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    AUDITOK: 'success', // 审核通过
    AUDITING: 'processing', // 审核中
    TEMP: 'default', // 暂存
    AUDITNO: 'error', // 审核不通过
    BACK: 'warning', // 已撤回
    ENABLE: 'success', // 启用
    DISABLE: 'error', // 禁用
    CLOSED: 'default', // 关闭
    INTEND: 'blue' // 意向
  }
  return statusMap[status] || 'default'
}

const handleSwitch = (type) => {
  // 上一条
  if (type) {
    if (curIndex.value > 0) {
      curIndex.value--
      getDetailById(ids[curIndex.value])
    }
    return
  }
  if (curIndex.value < ids.length - 1) {
    curIndex.value++
    getDetailById(ids[curIndex.value])
  }
}

// 通过id获取详情
const requestFuncObj = computed(() => {
  const func = {
    delById: null,
    queryById: null
  }
  switch (detailData.value.trackingType) {
    case 'Idle':
      func.queryById = idleQueryById
      func.delById = idleDelById
      break
    case 'Occupy':
      func.queryById = occupyQueryById
      func.delById = occupyDelById
      break
    case 'Borrow':
      func.queryById = borrowQueryById
      func.delById = borrowDelById
      break
    case 'Self':
      func.queryById = selfQueryById
      func.delById = selfDelById
      break
  }
  return func
})
// 获取详情
const getDetailById = async (id) => {
  const { result } = await requestFuncObj.value.queryById(id)
  detailData.value = result
}

// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '提示',
    content: '确认删除当前资产？',
    centered: true,
    onOk: async () => {
      await requestFuncObj.value.delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      visible.value = false
    }
  })
}
</script>
