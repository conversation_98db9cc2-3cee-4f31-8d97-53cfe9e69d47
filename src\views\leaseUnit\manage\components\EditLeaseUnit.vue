<template>
  <a-drawer
    v-model:open="visible"
    class="edit-lease-unit-drawer common-drawer"
    :title="`${formData.id ? '编辑' : '添加'}租赁单元`"
    placement="right"
    width="1500px"
    :mask-closable="false"
  >
    <div class="steps"><a-steps :current="currentStep" :items="items"></a-steps></div>

    <a-spin :spinning="confirmLoading">
      <div v-show="currentStep === 0">
        <h2 class="text-[16px] font-bold my-[24px]">租赁单元基础信息</h2>
        <a-form ref="basicFormRef" :model="formData" :rules="rules" :label-col="{ span: 4 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="单元名称" name="name" required>
                <a-input v-model:value="formData.name" placeholder="请输入单元名称" show-count :maxlength="50" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="资产产权" name="virtualLeaseUnit">
                <a-radio-group v-model:value="formData.virtualLeaseUnit">
                  <a-radio :value="false">无产权</a-radio>
                  <a-radio :value="true">有产权</a-radio>
                </a-radio-group>
                <house-owner-selector
                  v-if="formData.virtualLeaseUnit === true"
                  v-model="formData.houseOwner"
                  v-model:display-value="formData.houseOwner_dictText"
                  placeholder="选择房屋"
                  :style="{ width: '200px' }"
                  @change="handleHouseOwnerChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="产权用途" name="propertyUse">
                <dict-select
                  v-model="formData.propertyUse"
                  placeholder="产权用途"
                  code="CT_BAS_PropertyUse"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="所属项目" name="wyProject">
                <a-cascader
                  v-model:value="formData.wyProjectArray"
                  :options="projectOptions"
                  :field-names="{ label: 'label', value: 'value', children: 'children' }"
                  :load-data="loadBuildingFloorData"
                  placeholder="请选择项目/楼栋/楼层"
                  @change="handleProjectChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="地址" required>
                <a-form-item name="pcaCodeArray" :no-style="true">
                  <a-cascader
                    v-model:value="formData.pcaCodeArray"
                    placeholder="选择省市区"
                    class="pca-code"
                    :options="region"
                    @change="handlePcaCodeChange"
                    style="width: 50%"
                  />
                </a-form-item>
                <a-form-item name="detailAddress" :no-style="true">
                  <a-input
                    v-model:value="formData.detailAddress"
                    placeholder="请输入详细地址"
                    class="detail-address"
                    style="width: 50%"
                  />
                </a-form-item>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="资产类型" name="assetType" required>
                <dict-select
                  v-model="formData.assetType"
                  placeholder="资产类型"
                  code="CT_BASE_ENUM_LeaseUnit_AssetType"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="资产权属公司" name="ownerCompany" required>
                <dept-tree-select
                  v-model="formData.ownerCompany"
                  placeholder="请选择资产权属公司"
                  type="company"
                ></dept-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="租金归集公司" name="collectionCompany" required>
                <dept-tree-select
                  v-model="formData.collectionCompany"
                  placeholder="请选择租金归集公司"
                  type="company"
                ></dept-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="物业管理公司" name="manageCompany" required>
                <dept-tree-select
                  v-model="formData.manageCompany"
                  placeholder="请选择物业管理公司"
                  type="company"
                ></dept-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="使用权类型" name="landNature" required>
                <dict-select
                  v-model="formData.landNature"
                  placeholder="使用权类型"
                  code="CT_BAS_LandNature"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="租赁单元类别" name="treeId" required>
                <api-tree-select
                  v-model="formData.treeId"
                  :async-fn="getLeaseUnitTree"
                  placeholder="请选择租赁单元类别"
                ></api-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="配套设施" name="supportFacility" :label-col="{ span: 2 }">
                <a-textarea
                  v-model:value="formData.supportFacility"
                  placeholder="配套设施"
                  :rows="4"
                  show-count
                  :maxlength="500"
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="备注" name="remark" :label-col="{ span: 2 }">
                <a-textarea v-model:value="formData.remark" placeholder="备注" :rows="4" show-count :maxlength="500" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <div v-show="currentStep === 1">
        <h2 class="text-[16px] font-bold my-[24px]">租赁信息</h2>
        <a-form ref="leaseFormRef" :model="formData" :rules="rules" :label-col="{ span: 5 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="使用类型" name="useType" required>
                <dict-select v-model="formData.useType" placeholder="使用类型" code="CT_BAS_UseType"></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="租赁面积(m²)" name="leaseArea" required>
                <a-input-number
                  v-model:value="formData.leaseArea"
                  :precision="2"
                  style="width: 100%"
                  addon-after="m²"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="租赁用途" name="leaseUse" required>
                <dict-select v-model="formData.leaseUse" placeholder="租赁用途" code="CT_BAS_LeaseUse"></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="片区管理员" name="areaManager">
                <user-select
                  v-model="formData.areaManager"
                  v-model:display-value="formData.areaManager_dictText"
                  placeholder="请选择片区管理员"
                  title="请选择片区管理员"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="生效日期" name="effectDate" required>
                <a-date-picker v-model:value="formData.effectDate" value-format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="到期日期" name="expireDate" required>
                <a-date-picker v-model:value="formData.expireDate" value-format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <div v-show="currentStep === 2">
        <h2 class="text-[16px] font-bold my-[24px]">建筑信息</h2>
        <a-form ref="landFormRef" :model="formData" :rules="rules" :label-col="{ span: 5 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="房产类型" name="houseType">
                <dict-select
                  v-model="formData.houseType"
                  placeholder="房产类型"
                  code="CT_BASE_ENUM_HouseOwner_HouseType"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="建筑面积(m²)" name="structureArea" required>
                <a-input-number
                  v-model:value="formData.structureArea"
                  :precision="2"
                  style="width: 100%"
                  addon-after="m²"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="宗地面积(m²)" name="floorArea">
                <a-input-number
                  v-model:value="formData.floorArea"
                  :precision="2"
                  style="width: 100%"
                  addon-after="m²"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="建筑结构" name="buildStructrue" required>
                <dict-select
                  v-model="formData.buildStructrue"
                  placeholder="建筑结构"
                  code="CT_BAS_BuildStructrue"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="建筑年份" name="buildYear">
                <a-date-picker
                  v-model:value="formData.buildYear"
                  picker="year"
                  value-format="YYYY"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="层数/总层数" name="layerNum">
                <a-input-group compact>
                  <a-input v-model:value="formData.currentLayer" style="width: 45%" placeholder="层数" />
                  <a-input
                    v-model:value="splitValue"
                    class="site-input-split"
                    style="width: 30px; border-left: 0; pointer-events: none"
                    placeholder="/"
                    disabled
                  />
                  <a-input
                    v-model:value="formData.totalLayer"
                    class="site-input-right"
                    style="width: 45%"
                    placeholder="总层数"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="层高(m)" name="layerHight">
                <a-input-number
                  v-model:value="formData.layerHight"
                  :precision="2"
                  style="width: 100%"
                  addon-after="m"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="户型" name="houseModel">
                <a-input v-model:value="formData.houseModel" placeholder="户型" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="消防等级" name="firefightingRate">
                <dict-select
                  v-model="formData.firefightingRate"
                  placeholder="消防等级"
                  code="CT_BAS_FirefightingRate"
                ></dict-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="房屋安全等级" name="houseSafeRate" required>
                <dict-select
                  v-model="formData.houseSafeRate"
                  placeholder="房屋安全等级"
                  code="CT_BAS_HouseSafeRate"
                ></dict-select>
              </a-form-item>
            </a-col>
          </a-row>

          <h2 class="text-[16px] font-bold my-[24px]">税务信息</h2>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="房产税计税原值" name="houseTaxOrgValue">
                <a-input-number v-model:value="formData.houseTaxOrgValue" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="开票增值税率(%)" name="addTaxRate">
                <a-input-number v-model:value="formData.addTaxRate" style="width: 100%" addon-after="%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="发票地址" name="invoiceAddress">
                <a-input v-model:value="formData.invoiceAddress" placeholder="发票地址" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <div v-show="currentStep === 3">
        <h2 class="text-[16px] font-bold my-[24px]">水电信息</h2>
        <water-electric-table v-model="formData.waterElectricList" />
      </div>
      <div v-show="currentStep === 4">
        <h2 class="text-[16px] font-bold my-[24px]">附件</h2>
        <div class="p-4">
          <files-upload v-model="formData.files" />
        </div>
      </div>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button :disabled="currentStep <= 0" @click="handlePrevStep">上一步</a-button>
      <a-button :disabled="currentStep >= items.length - 1" @click="handleNextStep">下一步</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import region from '@/json/region.json'
import { projectPage, queryBuilding } from '@/views/projects/apis.js'
import { queryFloor } from '@/views/building/apis/building.js'
import { addLeaseUnit, submitLeaseUnit, editLeaseUnit, editLeaseUnitPut } from '../apis/leaseUnit'
import { getLeaseUnitTree } from '../apis/leaseUnitTree'
import HouseOwnerSelector from './HouseOwnerSelector.vue'
import WaterElectricTable from './WaterElectricTable.vue'
import FilesUpload from '@/components/FilesUpload.vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)
const currentStep = ref(0)
const splitValue = ref('/')
const confirmLoading = ref(false)
const projectOptions = ref([])

const basicFormRef = ref()
const leaseFormRef = ref()
const landFormRef = ref()

// 步骤配置
const items = [
  { title: '基础信息' },
  { title: '租赁信息' },
  { title: '土地及建筑物信息' },
  { title: '水电费用' },
  { title: '附件信息' }
]

// 表单验证工具函数
const validatePcaCodeArray = (rule, value) => {
  if (!value) return Promise.reject('请选择省市区')
  if (!Array.isArray(value) || value.length === 0) return Promise.reject('请选择完整的省市区信息')
  if (value.length < 3) return Promise.reject('请选择到区级行政区')
  return Promise.resolve()
}

const validateDetailAddress = (rule, value) => {
  if (!value || value.trim() === '') return Promise.reject('请输入详细地址')
  return Promise.resolve()
}

// 表单默认值
const defaultFormData = reactive({
  id: undefined,
  name: undefined,
  virtualLeaseUnit: false,
  houseOwner: undefined,
  houseOwner_dictText: undefined,
  propertyUse: undefined,
  wyProject: undefined,
  wyBuilding: undefined,
  wyFloor: undefined,
  wyProjectArray: [],
  province: undefined,
  city: undefined,
  area: undefined,
  pcaCode: undefined,
  pcaCodeArray: [],
  detailAddress: undefined,
  assetType: undefined,
  ownerCompany: undefined,
  collectionCompany: undefined,
  manageCompany: undefined,
  landNature: undefined,
  treeId: undefined,
  supportFacility: undefined,
  remark: undefined,
  useType: undefined,
  leaseArea: undefined,
  leaseUse: undefined,
  areaManager: undefined,
  effectDate: undefined,
  expireDate: undefined,
  houseType: undefined,
  structureArea: undefined,
  floorArea: undefined,
  buildStructrue: undefined,
  buildYear: undefined,
  layerNum: undefined,
  layerHight: undefined,
  houseModel: undefined,
  firefightingRate: undefined,
  houseSafeRate: undefined,
  houseTaxOrgValue: undefined,
  addTaxRate: undefined,
  invoiceAddress: undefined,
  waterElectricList: [],
  attachmentIds: undefined,
  files: [],
  currentLayer: undefined,
  totalLayer: undefined
})

const formData = reactive({ ...defaultFormData })

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入单元名称', trigger: 'blur' },
    { min: 2, max: 50, message: '单元名称长度应为2-50个字符', trigger: 'blur' }
  ],
  pcaCodeArray: [{ required: true, validator: validatePcaCodeArray, trigger: 'change' }],
  detailAddress: [{ required: true, validator: validateDetailAddress, trigger: ['blur', 'change'] }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  collectionCompany: [{ required: true, message: '请选择租金归集公司', trigger: 'change' }],
  ownerCompany: [{ required: true, message: '请选择资产权属公司', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  landNature: [{ required: true, message: '请选择使用权类型', trigger: 'change' }],
  treeId: [{ required: true, message: '请选择租赁单元分类', trigger: 'change' }],
  supportFacility: [{ max: 500, message: '配套设施描述不能超过500个字符', trigger: 'blur' }],
  remark: [{ max: 500, message: '备注不能超过500个字符', trigger: 'blur' }],
  useType: [{ required: true, message: '请选择使用类型', trigger: 'change' }],
  leaseArea: [
    { required: true, message: '请输入租赁面积', trigger: 'blur' },
    { type: 'number', min: 0, message: '租赁面积不能为负数', trigger: 'blur' }
  ],
  leaseUse: [{ required: true, message: '请选择租赁用途', trigger: 'change' }],
  effectDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
  expireDate: [
    { required: true, message: '请选择到期日期', trigger: 'change' },
    {
      validator: (rule, value) => {
        if (!value || !formData.effectDate) return Promise.resolve()
        return value >= formData.effectDate ? Promise.resolve() : Promise.reject('到期日期不能早于生效日期')
      },
      trigger: 'change'
    }
  ],
  structureArea: [
    { required: true, message: '请输入建筑面积', trigger: 'blur' },
    { type: 'number', min: 0, message: '建筑面积不能为负数', trigger: 'blur' }
  ],
  floorArea: [{ type: 'number', min: 0, message: '宗地面积不能为负数', trigger: 'blur' }],
  buildStructure: [{ required: true, message: '请选择建筑结构', trigger: 'change' }],
  currentLayer: [{ pattern: /^\d*$/, message: '层数必须为非负整数', trigger: 'blur' }],
  totalLayer: [{ pattern: /^\d*$/, message: '总层数必须为非负整数', trigger: 'blur' }],
  layerHight: [{ type: 'number', min: 0, message: '层高不能为负数', trigger: 'blur' }],
  houseSafeRate: [{ required: true, message: '请选择房屋安全等级', trigger: 'change' }],
  houseTaxOrgValue: [{ type: 'number', min: 0, message: '房产税计税原值不能为负数', trigger: 'blur' }],
  addTaxRate: [{ type: 'number', min: 0, message: '开票增值税率不能为负数', trigger: 'blur' }]
}

/**
 * 打开弹窗并初始化表单数据
 * @param {Object} data - 租赁单元数据
 */
const open = async (data) => {
  visible.value = true

  if (data?.id) {
    Object.assign(formData, data)
    formData.propertyUse = formData.propertyUse || undefined

    // 处理资产数据
    if (formData.virtualLeaseUnit !== true) {
      formData.houseOwner = undefined
      formData.houseOwner_dictText = undefined
    }

    // 处理层数数据
    if (formData.layerNum) {
      const [currentLayer = '', totalLayer = ''] = formData.layerNum.split('/')
      Object.assign(formData, { currentLayer, totalLayer })
    }

    // 处理省市区数据
    formData.pcaCodeArray = formData.pcaCode?.split(',').filter(Boolean) || []

    // 处理水电表数据
    formData.waterElectricList = Array.isArray(formData.waterElectricList) ? formData.waterElectricList : []

    // 处理项目楼栋楼层数据
    if (formData.wyProject || formData.wyBuilding || formData.wyFloor) {
      await initProjectData()
    }
  } else {
    Object.assign(formData, defaultFormData)
    formData.waterElectricList = []
    formData.areaManager = undefined
  }

  // 加载项目列表
  await loadProjectList()
}

/**
 * 初始化项目数据
 */
const initProjectData = async () => {
  // 加载楼栋数据
  if (formData.wyProject) {
    const buildingRes = await queryBuilding({ id: formData.wyProject })
    const projectOption = projectOptions.value.find((item) => item.value === formData.wyProject)

    if (projectOption && buildingRes?.result) {
      projectOption.children = buildingRes.result.map((item) => ({
        value: item.id,
        label: item.name,
        isLeaf: false
      }))

      // 加载楼层数据
      if (formData.wyBuilding) {
        const floorRes = await queryFloor({ id: formData.wyBuilding })
        const buildingOption = projectOption.children.find((item) => item.value === formData.wyBuilding)

        if (buildingOption && floorRes?.result) {
          buildingOption.children = floorRes.result.map((item) => ({
            value: item.id,
            label: item.name,
            isLeaf: true
          }))
        }
      }
    }
  }

  // 设置选中值
  nextTick(() => {
    formData.wyProjectArray = [formData.wyProject, formData.wyBuilding, formData.wyFloor].filter(Boolean)
  })
}

/**
 * 加载项目列表数据
 */
const loadProjectList = async () => {
  const { result } = await projectPage()
  projectOptions.value =
    result?.records?.map((item) => ({
      value: item.id,
      label: item.name,
      isLeaf: false
    })) || []

  // 如果有选中的项目，重新加载项目数据
  if (formData.wyProject) {
    await initProjectData()
  }
}

/**
 * 级联加载楼栋和楼层数据
 * @param {Array} selectedOptions - 级联选择的选项数组
 */
const loadBuildingFloorData = async (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  targetOption.loading = true

  try {
    const isLoadingBuilding = selectedOptions.length === 1
    const api = isLoadingBuilding ? queryBuilding : queryFloor
    const res = await api({ id: targetOption.value })

    if (res?.result) {
      targetOption.children = res.result.map((item) => ({
        value: item.id,
        label: item.name,
        isLeaf: !isLoadingBuilding
      }))
    }
  } finally {
    targetOption.loading = false
  }
}

/**
 * 处理资产选择变更
 * @param {Object} selectItem - 选中的资产对象
 */
const handleHouseOwnerChange = (selectItem) => {
  if (!selectItem) {
    formData.houseOwner = undefined
    return
  }
  formData.houseOwner = selectItem.id
  // 关联项目-楼栋-楼层
  formData.wyProject = selectItem.wyProject
  formData.wyBuilding = selectItem.wyBuilding
  formData.wyFloor = selectItem.wyFloor
  formData.wyProjectArray = [formData.wyProject, formData.wyBuilding, formData.wyFloor].filter(Boolean)
  // 关联省市区
  formData.province = selectItem.province
  formData.city = selectItem.city
  formData.area = selectItem.area
  formData.pcaCodeArray = [formData.province, formData.city, formData.area].filter(Boolean)
  // 关联详细地址
  formData.detailAddress = selectItem.detailAddress
}

/**
 * 处理省市区选择变更
 * @param {Array} value - 选中的省市区代码数组
 * @param {Array} selectedOptions - 选中的省市区选项数组
 */
const handlePcaCodeChange = (value, selectedOptions) => {
  formData.pcaCode = value.join(',')
  ;[formData.province, formData.city, formData.area] = selectedOptions.map((opt) => opt?.name || '')
}

/**
 * 处理项目楼栋楼层选择变更
 * @param {Array} value - 选中的项目楼栋楼层ID数组
 */
const handleProjectChange = (value) => {
  formData.wyProjectArray = value
  ;[formData.wyProject, formData.wyBuilding, formData.wyFloor] = value
}

/**
 * 下一步操作
 */
const handleNextStep = async () => {
  if (currentStep.value >= items.length - 1) return

  try {
    switch (currentStep.value) {
      case 0:
        await basicFormRef.value?.validate()
        break
      case 1:
        await leaseFormRef.value?.validate()
        break
      case 2:
        await landFormRef.value?.validate()
        break
    }
    currentStep.value++
  } catch (error) {
    message.error('请填写完必填项后再进入下一步')
  }
}

/**
 * 上一步操作
 */
const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

/**
 * 保存并提交租赁单元
 */
const handleSave = async () => {
  if (confirmLoading.value) return

  try {
    confirmLoading.value = true

    // 验证所有表单
    await Promise.all([basicFormRef.value?.validate(), leaseFormRef.value?.validate(), landFormRef.value?.validate()])
  } catch (error) {
    console.error('表单验证失败:', error)
    message.error('请检查并填写完所有必填项')
    confirmLoading.value = false
    return
  }
  const api = formData.id ? editLeaseUnit : submitLeaseUnit
  try {
    const res = await api(formData)
  } finally {
    confirmLoading.value = false
  }

  message.success(`租赁单元${formData.id ? '编辑' : '添加'}成功`)
  visible.value = false
  emit('refresh', res?.result || formData)
  handleCancel()
}

/**
 * 暂存租赁单元
 */
const handleTemporaryStorage = async () => {
  if (confirmLoading.value) return

  try {
    confirmLoading.value = true
    const api = formData.id ? editLeaseUnitPut : addLeaseUnit
    const result = await api(formData)

    message.success('租赁单元暂存成功')
    visible.value = false
    emit('refresh', result?.result || formData)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 取消操作，重置表单
 */
const handleCancel = () => {
  visible.value = false
  currentStep.value = 0
  Object.assign(formData, defaultFormData)
  formData.files = []
  basicFormRef.value?.resetFields()
  leaseFormRef.value?.resetFields()
  landFormRef.value?.resetFields()
}

// 监听是否有产权状态变化
watch(
  () => formData.virtualLeaseUnit,
  (newVal, oldVal) => {
    if (oldVal === true && newVal === false) {
      formData.houseOwner = ''
      formData.houseOwner_dictText = undefined
    }
  }
)

// 监听层数变化
watch([() => formData.currentLayer, () => formData.totalLayer], ([currentLayer, totalLayer]) => {
  formData.layerNum = currentLayer || totalLayer ? `${currentLayer || ''}/${totalLayer || ''}` : ''
})

// 监听文件列表变化，更新 attachmentIds
watch(
  () => formData.files,
  (newFiles) => {
    if (Array.isArray(newFiles)) {
      formData.attachmentIds = newFiles.join(',')
    } else if (typeof newFiles === 'string') {
      formData.attachmentIds = newFiles
    } else {
      formData.attachmentIds = ''
    }
  },
  { immediate: true, deep: true }
)

// 导出组件方法
defineExpose({ open })
</script>
<style scoped lang="less">
.pca-code,
.detail-address {
  width: 50%;
}

.pca-code :deep(.ant-select-selector) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
}
.detail-address {
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.site-input-split {
  background-color: #fff;
}
.site-input-right {
  border-left-width: 0;
  &:hover,
  &:focus {
    border-left-width: 1px;
  }
}

.ant-input-rtl.site-input-right {
  border-right-width: 0;
  &:hover,
  &:focus {
    border-right-width: 1px;
  }
}
</style>
