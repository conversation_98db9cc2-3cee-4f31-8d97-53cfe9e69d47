<template>
  <div>
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '112px' } }" autocomplete="off">
      <a-form-item label="合同编号" name="contractNumber">
        <a-input v-model:value="form.contractNumber" :maxlength="50" show-count></a-input>
      </a-form-item>
      <a-form-item label="签约日期" name="signDate">
        <a-date-picker v-model:value="form.signDate" value-format="YYYY-MM-DD"></a-date-picker>
      </a-form-item>
      <a-form-item label="签约客户" name="customer">
        <a-form-item-rest>
          <customer-select v-model="form.customer"></customer-select>
        </a-form-item-rest>
      </a-form-item>
      <a-form-item label="合同类型" name="contractType">
        <dict-select v-model="form.contractType" code="CT_BAS_ContractType"></dict-select>
      </a-form-item>
      <a-form-item label="物业管理公司" name="manageCompany">
        <dept-tree-select v-model="form.manageCompany" type="company"></dept-tree-select>
      </a-form-item>
      <a-form-item label="业务员" name="operator">
        <a-form-item-rest>
          <user-select v-model="form.operator"></user-select>
        </a-form-item-rest>
      </a-form-item>
      <a-form-item label="业务部门" name="operatorDepart">
        <dept-tree-select v-model="form.operatorDepart"></dept-tree-select>
      </a-form-item>
      <a-form-item label="定价类型" name="pricedType">
        <dict-select v-model="form.pricedType" code="CT_BASE_ENUM_Contract_PricedType"></dict-select>
      </a-form-item>
      <a-form-item label="合同开始日期" name="startDate">
        <a-date-picker v-model:value="form.startDate" value-format="YYYY-MM-DD"></a-date-picker>
      </a-form-item>
      <a-form-item label="合同结束日期" name="expireDate">
        <a-date-picker v-model:value="form.expireDate" value-format="YYYY-MM-DD"></a-date-picker>
      </a-form-item>
    </a-form>
    <div class="flex items-center justify-between mt-[40px] mb-[12px]">
      <strong class="text-[16px]">租赁单元</strong>
      <a-button type="primary" size="medium" @click="handleAddUnit">
        <i class="a-icon-plus"></i>
        添加单元
      </a-button>
    </div>
    <a-table
      :data-source="form.contractLeaseUnitsList"
      :columns="columns"
      :pagination="false"
      :scroll="{ x: 1500, y: 'calc(100vh - 348px)' }"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn mr-[10px]" @click="viewUnitDetail(record)">单元详情</span>
          <a-popconfirm title="是否确认移除？" @confirm="handleRemoveUnit(index)">
            <span class="text-error cursor-pointer">移除</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <choose-unit ref="chooseUnitRef" @updateUnitList="updateUnitList"></choose-unit>
    <lease-unit-detail ref="leaseUnitDetailRef" :data-list="form.contractLeaseUnitsList"></lease-unit-detail>
  </div>
</template>

<script setup>
import ChooseUnit from '@/views/rentScheme/components/ChooseUnit.vue'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'
import { message } from 'ant-design-vue'

const { form } = defineProps({
  form: { type: Object, required: true }
})

const validateEndDate = (_, value) => {
  if (!value) return Promise.reject('请选择合同结束日期')
  if (new Date(value) < new Date(form.startDate)) {
    return Promise.reject('合同结束日期不得早于合同开始日期')
  }
  return Promise.resolve()
}
const rules = {
  contractNumber: [{ required: true, message: '请填写合同编号', trigger: 'blur' }],
  signDate: [{ required: true, message: '请选择签约日期', trigger: 'change' }],
  customer: [{ required: true, message: '请选择签约客户', trigger: 'change' }],
  contractType: [{ required: true, message: '请选择合同类型', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  pricedType: [{ required: true, message: '请选择定价类型', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择合同开始日期', trigger: 'change' }],
  expireDate: [{ required: true, validator: validateEndDate, trigger: 'change' }]
}

const formRef = ref()

const clearValidate = () => {
  formRef.value.clearValidate()
}

const columns = [
  {
    title: '租赁单元名称',
    dataIndex: 'name',
    fixed: 'left',
    customRender: ({ record }) => record.name || record.leaseUnit_dictText
  },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁面积m²', dataIndex: 'leaseArea' },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  { title: '消防等级', dataIndex: 'firefightingRate_dictText' },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const chooseUnitRef = ref()
const handleAddUnit = () => {
  chooseUnitRef.value.open([...form.contractLeaseUnitsList])
}
const updateUnitList = (list) => {
  form.contractLeaseUnitsList = list
}

const leaseUnitDetailRef = ref()
const viewUnitDetail = (data) => {
  leaseUnitDetailRef.value.open(data)
}

const handleRemoveUnit = (index) => {
  form.contractLeaseUnitsList.splice(index, 1)
}

const validate = async () => {
  await formRef.value.validate()
  if (!form.contractLeaseUnitsList.length) {
    message.warning('请选择租赁单元')
    return Promise.reject('请选择租赁单元')
  }
  return Promise.resolve()
}

defineExpose({ validate, clearValidate })
</script>
