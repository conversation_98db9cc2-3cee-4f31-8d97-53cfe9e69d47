<template>
  <div class="user-select">
    <a-input :value="displayValue" :placeholder="placeholder" readonly @click="handleClick" class="selector-input">
      <template #suffix>
        <i v-if="modelValue" class="a-icon-close-solid text-tertiary cursor-pointer" @click.stop="handleClear"></i>
        <i v-else class="a-icon-arrow-down"></i>
      </template>
    </a-input>
  </div>

  <a-modal
    v-model:open="visible"
    :title="title"
    width="800px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form autocomplete="off" layout="inline" class="!mb-[16px]">
      <a-form-item label="所属部门">
        <dept-tree-select
          v-model="searchParams.departId"
          placeholder="搜索所属部门"
          @change="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
          style="width: 220px"
        ></dept-tree-select>
      </a-form-item>
      <a-form-item label="用户账号">
        <a-input
          v-model:value="searchParams.username"
          placeholder="搜索用户账号"
          @input="handleInput"
          style="width: 220px"
          allow-clear
        >
          <template #prefix>
            <i class="a-icon-search text-primary"></i>
          </template>
        </a-input>
      </a-form-item>
    </a-form>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ y: '50vh' }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { renderDict } from '@/utils/render'
import { queryUserList } from '@/views/system/user/apis'

const { modelValue, displayValue, multiple } = defineProps({
  modelValue: { required: true, type: [String, Array] },
  displayValue: { type: String, default: '' },
  placeholder: { type: String, default: '' },
  title: { type: String, default: '选择人员' },
  multiple: { type: Boolean, default: false },
  maxCount: { type: Number, default: undefined }
})

const emit = defineEmits(['update:modelValue', 'update:displayValue', 'change'])

const searchParams = reactive({
  username: undefined,
  departId: undefined
})

const { list, pagination, tableLoading, onTableFetch } = usePageTable(queryUserList)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

const columns = [
  { title: '账号', dataIndex: 'username', width: 120, fixed: 'left' },
  { title: '姓名', dataIndex: 'realname', width: 100 },
  { title: '性别', dataIndex: 'sex', width: 80, customRender: ({ text }) => renderDict(text, 'sex') },
  { title: '手机', dataIndex: 'phone', width: 120 },
  { title: '部门', dataIndex: 'orgCodeTxt' }
]

const visible = ref(false)

const handleClick = () => {
  visible.value = true
  if (!list.value.length) {
    onTableChange()
  }
}

const handleClear = () => {
  emit('update:modelValue', multiple ? [] : '')
  emit('update:displayValue', '')
  emit('change', multiple ? [] : '')
}

const handleConfirm = () => {
  const value = multiple ? selectedRowKeys.value : selectedRowKeys.value[0]
  emit('update:modelValue', value)
  emit('update:displayValue', selectedRows.value.map((item) => item.realname).join(', '))
  emit('change', value)
  handleCancel()
}

const handleCancel = () => {
  visible.value = false
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      pageNo: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

watch(
  () => modelValue,
  async (newVal) => {
    if (newVal) {
      let selectedUnit = list.value.find((item) => item.id === newVal)

      if (!selectedUnit) {
        await onTableFetch({ pageNo: 1, pageSize: pagination.value.pageSize })
        selectedUnit = list.value.find((item) => item.id === newVal)
      }

      if (selectedUnit) {
        selectedRowKeys.value = [selectedUnit.id]
        selectedRows.value = [selectedUnit]
        emit('update:displayValue', selectedRows.value.map((item) => item.realname).join(', '))
      }
    } else {
      emit('update:displayValue', undefined)
      clearSelection()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.user-select {
  display: inline-block;
  width: 100%;
}

.selector-input {
  cursor: pointer;
  background-color: #fff;
}

.selector-input :deep(.ant-input) {
  cursor: pointer;
}

.selector-input :deep(.ant-input-suffix) {
  display: flex;
  align-items: center;
}

.selector-input :deep(.a-icon-arrow-down) {
  color: rgba(0, 0, 0, 0.25);
  font-size: 12px;
  margin-right: 6px;
  transition: transform 0.3s;
}

.selector-input:hover :deep(.a-icon-arrow-down) {
  color: #000;
}
</style>
