import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

export const getLeaseUnitList = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnit/list',
    params
  })
}

// 保存
export const submitLeaseUnit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/submit',
    data
  })
}

// 暂存
export const addLeaseUnit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/add',
    data
  })
}

// 编辑
export const editLeaseUnit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/edit',
    data
  })
}

// 编辑暂存
export const editLeaseUnitPut = (data) => {
  return request({
    method: 'put',
    url: '/bas/leaseUnit/edit',
    data
  })
}

// 通过 id 删除
export const deleteLeaseUnit = (params) => {
  return request({
    method: 'delete',
    url: '/bas/leaseUnit/delete',
    params
  })
}

// 批量删除
export const batchDeleteLeaseUnit = (params) => {
  return request({
    method: 'delete',
    url: '/bas/leaseUnit/deleteBatch',
    params
  })
}

// 导出 Excel
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 导入 Excel
export const importExcel = (data, controller) => {
  return advanceUpload('/bas/leaseUnit/importExcel', data, controller)
}

// 审批
export const audit = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnit/audit',
    params
  })
}

// 反审批
export const unAudit = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnit/unAudit',
    params
  })
}

// 获取租赁单元详情
export const getLeaseUnitDetail = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnit/queryById',
    params
  })
}
