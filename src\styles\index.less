/* 自定义滚动条样式 */
.scrollbar {
  /* 设置整个滚动条的宽度 */
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  /* 设置滚动条轨道的样式 */
  &::-webkit-scrollbar-track {
    border-radius: 8px;
    background-color: #fff;
  }

  /* 设置滚动条滑块的样式 */
  &::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: #888;
  }

  /* 鼠标悬停在滑块上时的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
}

/* 隐藏滚动条 */
.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.ant-btn {
  & > i {
    margin-right: 8px;
  }
  & + .ant-btn {
    margin-left: 16px;
  }
}

.primary-btn {
  cursor: pointer;
  color: var(--color-primary);
  & + .primary-btn {
    margin-left: 8px;
  }
}

.ant-pagination {
  .ant-select-selector {
    width: 100px !important;
  }
}

.common-modal {
  .ant-modal-content {
    padding: 0;
  }
  .ant-modal-header {
    padding: 24px;
    margin-bottom: 0;
  }
  .ant-modal-title {
    font-size: 18px;
    font-weight: 700;
    line-height: 22px;
  }
  .ant-modal-close {
    top: 24px;
    right: 24px;
  }
  .ant-modal-body {
    max-height: 60vh;
    padding: 0 24px;
    overflow-y: auto;
  }
  .ant-modal-footer {
    margin-top: 0;
    padding: 24px;
    .ant-btn {
      padding: 4px 30px;
    }
  }
}

.ant-modal.ant-modal-confirm {
  .ant-modal-content {
    padding: 24px;
  }
  .ant-modal-confirm-title {
    font-size: 18px;
    font-weight: 700;
  }
  .anticon {
    display: none;
  }
  .ant-modal-confirm-body {
    display: block;
  }
  .ant-modal-confirm-content {
    margin-inline-start: 0 !important;
    margin-block-start: 24px !important;
    margin-block-end: 24px;
    max-width: 100% !important;
  }
  .ant-modal-confirm-btns {
    margin-top: 0;
    .ant-btn {
      height: 40px;
      padding: 4px 30px;
    }
  }
}

.common-drawer {
  .ant-drawer-header {
    height: 64px;
    padding: 0 24px 0 40px;
    position: relative;
    border-bottom: 1px solid #e6e9f0;
    flex: none;
  }
  .ant-drawer-body {
    padding: 24px 40px;
  }
  .ant-drawer-title {
    font-size: 18px;
    font-weight: 700;
  }
  .ant-drawer-close {
    position: absolute;
    right: 24px;
    top: 50%;
    transform: translateY(-50%);
  }
  .ant-drawer-footer {
    padding: 16px 40px;
    border-top: 1px solid #e6e9f0;
    .ant-btn {
      height: 40px;
      padding: 4px 30px;
    }
  }
}
.common-detail-drawer {
  .common-drawer();
  .ant-drawer-header {
    width: 100%;
    padding: 0 58px 0 24px;
    font-size: 14px;
  }
  .ant-drawer-close {
    margin-inline-end: 0;
  }
  .ant-drawer-header-title {
    flex: 0;
  }
  .ant-drawer-extra {
    flex: 1;
  }
}

.ant-form-item .ant-form-item-label > label {
  margin-right: 8px;
  &::after {
    content: none;
  }
}

.ant-input-textarea-show-count {
  position: relative;
  &::after {
    position: absolute;
    right: 1px;
    bottom: 1px;
    background-color: #fff;
    padding: 4px;
    border-bottom-right-radius: 8px;
  }
}

// #region ant-tabs样式设置区开始
.ant-tabs {
  .ant-tabs-tab + .ant-tabs-tab {
    margin: 0 0 0 24px;
  }
}
.ant-tabs-large {
  .ant-tabs-nav .ant-tabs-tab {
    font-size: 16px;
  }
}
.ant-tabs-tab {
  padding: 0 0 12px 0 !important;
}
.ant-tabs-tab-active {
  background: linear-gradient(180deg, rgba(242, 246, 254, 0), #f2f6fe 100%) !important;
}
.ant-tabs-nav {
  margin: 0 !important;
}
// #endregion ant-tabs样式设置区结束

@keyframes loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.a-icon-loading {
  display: inline-block;
  animation: loading 2s linear infinite;
  -webkit-animation: loading 2s linear infinite;
}

.ant-btn-primary:disabled {
  background-color: #b6cdfa;
  color: #fff;
  border: none;
}

// #region 表格样式设置区开始
.ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before,
.ant-table-wrapper
  .ant-table-thead
  > tr
  > td:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
  content: none;
}

.ant-table-wrapper .ant-table {
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #e6e9f0;
}

.ant-table-wrapper .ant-table-thead > tr {
  & > th,
  & > td {
    background: #fff;
    color: var(--color-tertiary);
    font-weight: normal;
  }
}

.ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr:last-child > td {
  border-bottom: 1px solid transparent;
}

.ant-table-wrapper .ant-table-thead tr {
  .ant-table-cell-fix-left-last {
    border-right: 1px solid #e6e9f0;
  }
  .ant-table-cell-fix-right-first {
    border-left: 1px solid #e6e9f0;
  }
}

.ant-table-wrapper .ant-table-tbody tr {
  .ant-table-cell-fix-left-last {
    border-right: 1px solid #e6e9f0;
  }
  .ant-table-cell-fix-right-first {
    border-left: 1px solid #e6e9f0;
  }
}
.ant-table-wrapper .ant-table-body {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: #fff;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #d7dae0;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: #d7dae0;
  }
}

.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td,
.ant-table-wrapper .ant-table-tbody > tr > td.ant-table-cell-row-hover {
  background: #eaf0fe;
}

.ant-table-wrapper .ant-table-tbody > tr.ant-table-row-selected > td {
  background: #f7f8fa;
}
.ant-table-wrapper .ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background: #f7f8fa;
}
// #endregion 表格样式设置区结束
